@echo off
echo ========================================
echo    HOOKAH ADS - PORTABLE BUILD SCRIPT
echo ========================================
echo.

echo Step 1: Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please download and install Node.js from https://nodejs.org/
    echo Then run this script again.
    pause
    exit /b 1
)
echo Node.js is installed ✓

echo.
echo Step 2: Installing dependencies...
echo This may take several minutes...
npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies!
    echo Trying to clean cache and retry...
    npm cache clean --force
    npm install
    if %errorlevel% neq 0 (
        echo FAILED: Could not install dependencies.
        pause
        exit /b 1
    )
)
echo Dependencies installed ✓

echo.
echo Step 3: Testing application...
echo Starting test run (will close automatically in 10 seconds)...
timeout /t 3 /nobreak >nul
start /min npm start
timeout /t 10 /nobreak >nul
taskkill /f /im electron.exe >nul 2>&1
echo Test completed ✓

echo.
echo Step 4: Building portable executable...
echo Attempting Method 1: electron-builder...
npm run build-portable
if %errorlevel% neq 0 (
    echo Method 1 failed, trying Method 2: electron-packager...
    npm install -g electron-packager
    electron-packager . hookah-ads --platform=win32 --arch=x64 --out=dist --overwrite --ignore=node_modules/electron-builder
    if %errorlevel% neq 0 (
        echo ERROR: Both build methods failed!
        echo Please check the error messages above.
        pause
        exit /b 1
    )
    echo Build completed with electron-packager ✓
    echo.
    echo Your executable is located at:
    echo dist\hookah-ads-win32-x64\hookah-ads.exe
) else (
    echo Build completed with electron-builder ✓
    echo.
    echo Your executable is located at:
    echo dist\HookahAds-Portable.exe
)

echo.
echo ========================================
echo           BUILD SUCCESSFUL!
echo ========================================
echo.
echo The portable executable has been created.
echo You can now copy it to any Windows computer and run it
echo without needing to install Node.js or any dependencies.
echo.
pause
