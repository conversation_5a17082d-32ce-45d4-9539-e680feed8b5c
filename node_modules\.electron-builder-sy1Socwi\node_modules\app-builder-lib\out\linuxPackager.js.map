{"version": 3, "file": "linuxPackager.js", "sourceRoot": "", "sources": ["../src/linuxPackager.ts"], "names": [], "mappings": ";;;AAAA,+CAAmC;AACnC,iCAAqD;AAGrD,yDAAqD;AAIrD,mEAA+D;AAE/D,2DAA4D;AAC5D,8CAAkD;AAElD,MAAa,aAAc,SAAQ,mCAAoC;IAGrE,YAAY,IAAc;;QACxB,KAAK,CAAC,IAAI,EAAE,eAAQ,CAAC,KAAK,CAAC,CAAA;QAE3B,MAAM,cAAc,GAAG,MAAA,IAAI,CAAC,4BAA4B,CAAC,cAAc,mCAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAA;QACrG,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAA,2BAAgB,EAAC,cAAc,CAAC,CAAA;IAC5H,CAAC;IAED,IAAI,aAAa;QACf,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;IAC7B,CAAC;IAED,aAAa,CAAC,OAAsB,EAAE,MAAmE;QACvG,IAAI,MAAgC,CAAA;QACpC,MAAM,SAAS,GAAG,GAAG,EAAE;YACrB,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;gBACnB,MAAM,GAAG,IAAI,qCAAiB,CAAC,IAAI,CAAC,CAAA;YACtC,CAAC;YACD,OAAO,MAAM,CAAA;QACf,CAAC,CAAA;QAED,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE,CAAC;YAC3B,IAAI,IAAI,KAAK,iBAAU,EAAE,CAAC;gBACxB,SAAQ;YACV,CAAC;YAED,MAAM,WAAW,GAA+F,CAAC,GAAG,EAAE;gBACpH,QAAQ,IAAI,EAAE,CAAC;oBACb,KAAK,UAAU;wBACb,OAAO,OAAO,CAAC,0BAA0B,CAAC,CAAC,OAAO,CAAA;oBACpD,KAAK,MAAM;wBACT,OAAO,OAAO,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAA;oBAC1C,KAAK,SAAS;wBACZ,OAAO,OAAO,CAAC,yBAAyB,CAAC,CAAC,OAAO,CAAA;oBACnD,KAAK,KAAK,CAAC;oBACX,KAAK,KAAK,CAAC;oBACX,KAAK,IAAI,CAAC;oBACV,KAAK,SAAS,CAAC;oBACf,KAAK,QAAQ,CAAC;oBACd,KAAK,KAAK,CAAC;oBACX,KAAK,KAAK;wBACR,OAAO,OAAO,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAA;oBAC/C;wBACE,OAAO,IAAI,CAAA;gBACf,CAAC;YACH,CAAC,CAAC,EAAE,CAAA;YAEJ,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;gBACpB,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;oBACzB,OAAO,IAAA,kCAAkB,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;gBAC/C,CAAC;gBAED,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,CAAA;YACzD,CAAC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;CACF;AA1DD,sCA0DC;AAED,SAAgB,oBAAoB,CAAC,IAAU;IAC7C,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,mBAAI,CAAC,GAAG;YACX,OAAO,QAAQ,CAAA;QACjB,KAAK,mBAAI,CAAC,IAAI;YACZ,OAAO,MAAM,CAAA;QACf,KAAK,mBAAI,CAAC,MAAM;YACd,OAAO,KAAK,CAAA;QACd,KAAK,mBAAI,CAAC,KAAK;YACb,OAAO,aAAa,CAAA;QAEtB;YACE,MAAM,IAAI,KAAK,CAAC,oBAAoB,IAAI,EAAE,CAAC,CAAA;IAC/C,CAAC;AACH,CAAC;AAdD,oDAcC", "sourcesContent": ["import { Arch } from \"builder-util\"\nimport { DIR_TARGET, Platform, Target } from \"./core\"\nimport { LinuxConfiguration } from \"./options/linuxOptions\"\nimport { Packager } from \"./packager\"\nimport { PlatformPackager } from \"./platformPackager\"\nimport AppImageTarget from \"./targets/AppImageTarget\"\nimport FlatpakTarget from \"./targets/FlatpakTarget\"\nimport FpmTarget from \"./targets/FpmTarget\"\nimport { LinuxTargetHelper } from \"./targets/LinuxTargetHelper\"\nimport SnapTarget from \"./targets/snap\"\nimport { createCommonTarget } from \"./targets/targetFactory\"\nimport { sanitizeFileName } from \"./util/filename\"\n\nexport class LinuxPackager extends PlatformPackager<LinuxConfiguration> {\n  readonly executableName: string\n\n  constructor(info: Packager) {\n    super(info, Platform.LINUX)\n\n    const executableName = this.platformSpecificBuildOptions.executableName ?? info.config.executableName\n    this.executableName = executableName == null ? this.appInfo.sanitizedName.toLowerCase() : sanitizeFileName(executableName)\n  }\n\n  get defaultTarget(): Array<string> {\n    return [\"snap\", \"appimage\"]\n  }\n\n  createTargets(targets: Array<string>, mapper: (name: string, factory: (outDir: string) => Target) => void): void {\n    let helper: LinuxTargetHelper | null\n    const getHelper = () => {\n      if (helper == null) {\n        helper = new LinuxTargetHelper(this)\n      }\n      return helper\n    }\n\n    for (const name of targets) {\n      if (name === DIR_TARGET) {\n        continue\n      }\n\n      const targetClass: typeof AppImageTarget | typeof SnapTarget | typeof FlatpakTarget | typeof FpmTarget | null = (() => {\n        switch (name) {\n          case \"appimage\":\n            return require(\"./targets/AppImageTarget\").default\n          case \"snap\":\n            return require(\"./targets/snap\").default\n          case \"flatpak\":\n            return require(\"./targets/FlatpakTarget\").default\n          case \"deb\":\n          case \"rpm\":\n          case \"sh\":\n          case \"freebsd\":\n          case \"pacman\":\n          case \"apk\":\n          case \"p5p\":\n            return require(\"./targets/FpmTarget\").default\n          default:\n            return null\n        }\n      })()\n\n      mapper(name, outDir => {\n        if (targetClass === null) {\n          return createCommonTarget(name, outDir, this)\n        }\n\n        return new targetClass(name, this, getHelper(), outDir)\n      })\n    }\n  }\n}\n\nexport function toAppImageOrSnapArch(arch: Arch): string {\n  switch (arch) {\n    case Arch.x64:\n      return \"x86_64\"\n    case Arch.ia32:\n      return \"i386\"\n    case Arch.armv7l:\n      return \"arm\"\n    case Arch.arm64:\n      return \"arm_aarch64\"\n\n    default:\n      throw new Error(`Unsupported arch ${arch}`)\n  }\n}\n"]}