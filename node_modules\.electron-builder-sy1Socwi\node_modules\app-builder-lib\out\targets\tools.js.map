{"version": 3, "file": "tools.js", "sourceRoot": "", "sources": ["../../src/targets/tools.ts"], "names": [], "mappings": ";;;AAAA,gDAA8C;AAE9C,SAAgB,iBAAiB;IAC/B,sCAAsC;IACtC,OAAO,IAAA,2BAAa,EAAC,aAAa,EAAE,aAAa,EAAE,0FAA0F,CAAC,CAAA;AAChJ,CAAC;AAHD,8CAGC", "sourcesContent": ["import { getBinFromUrl } from \"../binDownload\"\n\nexport function getLinuxToolsPath() {\n  //noinspection SpellCheckingInspection\n  return getBinFromUrl(\"linux-tools\", \"mac-10.12.3\", \"SQ8fqIRVXuQVWnVgaMTDWyf2TLAJjJYw3tRSqQJECmgF6qdM7Kogfa6KD49RbGzzMYIFca9Uw3MdsxzOPRWcYw==\")\n}\n"]}