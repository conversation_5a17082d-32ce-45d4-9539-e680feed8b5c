# Hookah Ads - Troubleshooting Guide

## Common Build Issues

### 1. "Node.js is not installed" Error
**Problem**: The build script can't find Node.js
**Solution**: 
- Download Node.js from https://nodejs.org/
- Install the LTS (Long Term Support) version
- Restart Command Prompt after installation
- Run BUILD-PORTABLE.bat again

### 2. "npm install" Fails
**Problem**: Dependencies won't install
**Solutions**:
```bash
# Try cleaning cache first
npm cache clean --force
npm install

# If still fails, try with different registry
npm install --registry https://registry.npmjs.org/

# Or try installing with legacy peer deps
npm install --legacy-peer-deps
```

### 3. "electron-builder" Command Not Found
**Problem**: Build tools not properly installed
**Solution**:
```bash
# Install electron-builder globally
npm install -g electron-builder

# Or use the packager method instead
npm install -g electron-packager
electron-packager . hookah-ads --platform=win32 --arch=x64 --out=dist --overwrite
```

### 4. Build Process Hangs or Takes Too Long
**Problem**: Build process seems stuck
**Solutions**:
- Wait longer (first build can take 10-15 minutes)
- Check internet connection
- Try building with verbose output:
```bash
npm run build-portable --verbose
```
- If still stuck, use Ctrl+C to cancel and try electron-packager method

### 5. Permission Errors
**Problem**: "Access denied" or permission errors
**Solutions**:
- Run Command Prompt as Administrator
- Make sure antivirus isn't blocking the process
- Close any running instances of the app

### 6. "Module not found" Errors
**Problem**: Missing dependencies after build
**Solution**:
```bash
# Delete node_modules and reinstall
rmdir /s /q node_modules
npm install
```

## Alternative Build Methods

### Method 1: Manual electron-packager
If BUILD-PORTABLE.bat fails, try this manually:
```bash
npm install
npm install -g electron-packager
electron-packager . hookah-ads --platform=win32 --arch=x64 --out=dist --overwrite
```

### Method 2: Using npx (no global install)
```bash
npm install
npx electron-packager . hookah-ads --platform=win32 --arch=x64 --out=dist --overwrite
```

### Method 3: Simple Node.js Bundle
If electron packaging fails, you can create a simple portable folder:
1. Copy the entire project folder to target computer
2. Install Node.js on target computer
3. Run `npm install` in the folder
4. Use START-ADS.bat to run the app

## Testing the Built Application

### 1. Test the Executable
- Copy the .exe file to a different location
- Double-click to run
- Verify both windows open (banner + control panel)
- Test basic functionality (add text, change colors)

### 2. Test on Different Computer
- Copy .exe to a computer without Node.js
- Should run without any additional installations
- If it doesn't work, the build may have failed

## File Locations After Build

### electron-builder Output:
- `dist/HookahAds-Portable.exe` - Single executable file

### electron-packager Output:
- `dist/hookah-ads-win32-x64/` - Folder with app files
- `dist/hookah-ads-win32-x64/hookah-ads.exe` - Main executable
- Copy the entire folder for distribution

## Getting Help

If you're still having issues:

1. **Check the error messages** - They usually tell you what's wrong
2. **Try the alternative methods** listed above
3. **Make sure you have a stable internet connection** during build
4. **Check that you have enough disk space** (at least 1GB free)
5. **Temporarily disable antivirus** during the build process

## Success Indicators

You know the build worked when:
- ✅ No error messages in the build process
- ✅ You find an .exe file in the dist folder
- ✅ The .exe runs when double-clicked
- ✅ Both banner and control windows open
- ✅ You can type text and see it in the banner

## Final Notes

- The first build always takes the longest
- Subsequent builds are much faster
- The portable .exe file will be 100-200MB in size
- This is normal for Electron applications
