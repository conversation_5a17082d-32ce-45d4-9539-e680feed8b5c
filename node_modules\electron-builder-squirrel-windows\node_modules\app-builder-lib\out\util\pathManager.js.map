{"version": 3, "file": "pathManager.js", "sourceRoot": "", "sources": ["../../src/util/pathManager.ts"], "names": [], "mappings": ";;;AAAA,6BAA4B;AAE5B,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AAE7C,SAAgB,eAAe,CAAC,IAAY;IAC1C,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAA;AAC3C,CAAC;AAFD,0CAEC;AAED,SAAgB,aAAa,CAAC,IAAa;IACzC,OAAO,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;AACnF,CAAC;AAFD,sCAEC", "sourcesContent": ["import * as path from \"path\"\n\nconst root = path.join(__dirname, \"..\", \"..\")\n\nexport function getTemplatePath(file: string) {\n  return path.join(root, \"templates\", file)\n}\n\nexport function getVendorPath(file?: string) {\n  return file == null ? path.join(root, \"vendor\") : path.join(root, \"vendor\", file)\n}\n"]}