{"version": 3, "file": "AppImageTarget.js", "sourceRoot": "", "sources": ["../../src/targets/AppImageTarget.ts"], "names": [], "mappings": ";;AAAA,+CAAoD;AACpD,uCAAqC;AACrC,uCAA+B;AAC/B,6BAA4B;AAC5B,kCAAgC;AAGhC,8DAA4E;AAC5E,mDAA0E;AAC1E,6CAA4D;AAE5D,6CAA6C;AAE7C,+FAA+F;AAC/F,MAAqB,cAAe,SAAQ,aAAM;IAIhD,YACE,OAAe,EACE,QAAuB,EACvB,MAAyB,EACjC,MAAc;QAEvB,KAAK,CAAC,UAAU,CAAC,CAAA;QAJA,aAAQ,GAAR,QAAQ,CAAe;QACvB,WAAM,GAAN,MAAM,CAAmB;QACjC,WAAM,GAAN,MAAM,CAAQ;QAPhB,YAAO,GAAoB,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,4BAA4B,EAAE,GAAI,IAAI,CAAC,QAAQ,CAAC,MAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAA;QAWhI,IAAI,CAAC,YAAY,GAAG,IAAI,eAAI,CAAS,GAAG,EAAE;;YACxC,MAAM,IAAI,GAAG,CAAA,MAAA,IAAI,CAAC,OAAO,CAAC,cAAc,0CAAE,IAAI,CAAC,GAAG,CAAC,KAAI,cAAc,CAAA;YACrE,OAAO,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,IAAI,KAAK,EAAE;gBACnE,oBAAoB,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE;aACzD,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,SAAiB,EAAE,IAAU;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,mEAAmE;QACnE,oEAAoE;QACpE,uDAAuD;QACvD,MAAM,YAAY,GAAG,QAAQ,CAAC,yBAAyB,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAClF,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QACzD,MAAM,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC;YAC3C,qBAAqB,EAAE,UAAU;YACjC,IAAI,EAAE,YAAY;YAClB,IAAI;SACL,CAAC,CAAA;QAEF,MAAM,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC1B,IAAI,CAAC,YAAY,CAAC,KAAK;YACvB,IAAI,CAAC,MAAM,CAAC,KAAK;YACjB,IAAA,iDAAgC,EAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,oDAAoD,CAAC;YAC5G,IAAA,oCAA0B,EAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAC3E,IAAA,2BAAc,EAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC;SACrC,CAAC,CAAA;QACF,MAAM,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QACpB,MAAM,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QAErB,MAAM,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QAC1B,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;YAC1B,MAAM,IAAA,qBAAU,EAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,gBAAgB,CAAC,EAAE,IAAA,8BAAe,EAAC,aAAa,CAAC,CAAC,CAAA;QACvH,CAAC;QAED,IACE,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,uBAAuB,IAAI,IAAI;YAC7D,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,uBAAuB,CAAC,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,EACzG,CAAC;YACD,OAAM;QACR,CAAC;QAED,MAAM,IAAI,GAAG;YACX,UAAU;YACV,SAAS;YACT,QAAQ,CAAC,GAAG;YACZ,QAAQ;YACR,mBAAI,CAAC,IAAI,CAAC;YACV,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,iBAAiB;YACjB,IAAI,CAAC,SAAS,CAAC;gBACb,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW;gBAC9C,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe;gBACtD,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC;gBAClB,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc;gBAC5C,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;gBACX,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB;gBAChD,GAAG,OAAO;aACX,CAAC;SACH,CAAA;QACD,IAAA,yBAAY,EAAC,IAAI,EAAE;YACjB,OAAO;SACR,CAAC,CAAA;QACF,IAAI,QAAQ,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YACvC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;QAClC,CAAC;QAED,MAAM,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC;YAC7C,IAAI,EAAE,YAAY;YAClB,gBAAgB,EAAE,QAAQ,CAAC,uBAAuB,CAAC,YAAY,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC;YACzF,MAAM,EAAE,IAAI;YACZ,IAAI;YACJ,QAAQ;YACR,iBAAiB,EAAE,IAAI;YACvB,UAAU,EAAE,MAAM,IAAA,oCAAuB,EAAC,IAAI,CAAC;SAChD,CAAC,CAAA;IACJ,CAAC;CACF;AA9FD,iCA8FC", "sourcesContent": ["import { Arch, serializeToYaml } from \"builder-util\"\nimport { outputFile } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { Target } from \"../core\"\nimport { LinuxPackager } from \"../linuxPackager\"\nimport { AppImageOptions } from \"../options/linuxOptions\"\nimport { getAppUpdatePublishConfiguration } from \"../publish/PublishManager\"\nimport { executeAppBuilderAsJson, objectToArgs } from \"../util/appBuilder\"\nimport { getNotLocalizedLicenseFile } from \"../util/license\"\nimport { LinuxTargetHelper } from \"./LinuxTargetHelper\"\nimport { createStageDir } from \"./targetUtil\"\n\n// https://unix.stackexchange.com/questions/375191/append-to-sub-directory-inside-squashfs-file\nexport default class AppImageTarget extends Target {\n  readonly options: AppImageOptions = { ...this.packager.platformSpecificBuildOptions, ...(this.packager.config as any)[this.name] }\n  private readonly desktopEntry: Lazy<string>\n\n  constructor(\n    ignored: string,\n    private readonly packager: LinuxPackager,\n    private readonly helper: LinuxTargetHelper,\n    readonly outDir: string\n  ) {\n    super(\"appImage\")\n\n    this.desktopEntry = new Lazy<string>(() => {\n      const args = this.options.executableArgs?.join(\" \") || \"--no-sandbox\"\n      return helper.computeDesktopEntry(this.options, `AppRun ${args} %U`, {\n        \"X-AppImage-Version\": `${packager.appInfo.buildVersion}`,\n      })\n    })\n  }\n\n  async build(appOutDir: string, arch: Arch): Promise<any> {\n    const packager = this.packager\n    const options = this.options\n    // https://github.com/electron-userland/electron-builder/issues/775\n    // https://github.com/electron-userland/electron-builder/issues/1726\n    // tslint:disable-next-line:no-invalid-template-strings\n    const artifactName = packager.expandArtifactNamePattern(options, \"AppImage\", arch)\n    const artifactPath = path.join(this.outDir, artifactName)\n    await packager.info.callArtifactBuildStarted({\n      targetPresentableName: \"AppImage\",\n      file: artifactPath,\n      arch,\n    })\n\n    const c = await Promise.all([\n      this.desktopEntry.value,\n      this.helper.icons,\n      getAppUpdatePublishConfiguration(packager, arch, false /* in any case validation will be done on publish */),\n      getNotLocalizedLicenseFile(options.license, this.packager, [\"txt\", \"html\"]),\n      createStageDir(this, packager, arch),\n    ])\n    const license = c[3]\n    const stageDir = c[4]\n\n    const publishConfig = c[2]\n    if (publishConfig != null) {\n      await outputFile(path.join(packager.getResourcesDir(stageDir.dir), \"app-update.yml\"), serializeToYaml(publishConfig))\n    }\n\n    if (\n      this.packager.packagerOptions.effectiveOptionComputed != null &&\n      (await this.packager.packagerOptions.effectiveOptionComputed({ desktop: await this.desktopEntry.value }))\n    ) {\n      return\n    }\n\n    const args = [\n      \"appimage\",\n      \"--stage\",\n      stageDir.dir,\n      \"--arch\",\n      Arch[arch],\n      \"--output\",\n      artifactPath,\n      \"--app\",\n      appOutDir,\n      \"--configuration\",\n      JSON.stringify({\n        productName: this.packager.appInfo.productName,\n        productFilename: this.packager.appInfo.productFilename,\n        desktopEntry: c[0],\n        executableName: this.packager.executableName,\n        icons: c[1],\n        fileAssociations: this.packager.fileAssociations,\n        ...options,\n      }),\n    ]\n    objectToArgs(args, {\n      license,\n    })\n    if (packager.compression === \"maximum\") {\n      args.push(\"--compression\", \"xz\")\n    }\n\n    await packager.info.callArtifactBuildCompleted({\n      file: artifactPath,\n      safeArtifactName: packager.computeSafeArtifactName(artifactName, \"AppImage\", arch, false),\n      target: this,\n      arch,\n      packager,\n      isWriteUpdateInfo: true,\n      updateInfo: await executeAppBuilderAsJson(args),\n    })\n  }\n}\n"]}