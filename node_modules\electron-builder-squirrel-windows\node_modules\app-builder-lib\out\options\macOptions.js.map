{"version": 3, "file": "macOptions.js", "sourceRoot": "", "sources": ["../../src/options/macOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["import { PlatformSpecificBuildOptions, TargetConfiguration, TargetSpecificOptions } from \"../index\"\nimport { CustomMacSign } from \"../macPackager\"\n\nexport type MacOsTargetName = \"default\" | \"dmg\" | \"mas\" | \"mas-dev\" | \"pkg\" | \"7z\" | \"zip\" | \"tar.xz\" | \"tar.lz\" | \"tar.gz\" | \"tar.bz2\" | \"dir\"\n\nexport interface MacConfiguration extends PlatformSpecificBuildOptions {\n  /**\n   * The application category type, as shown in the Finder via *View -> Arrange by Application Category* when viewing the Applications directory.\n   *\n   * For example, `\"category\": \"public.app-category.developer-tools\"` will set the application category to *Developer Tools*.\n   *\n   * Valid values are listed in [Apple's documentation](https://developer.apple.com/library/ios/documentation/General/Reference/InfoPlistKeyReference/Articles/LaunchServicesKeys.html#//apple_ref/doc/uid/TP40009250-SW8).\n   */\n  readonly category?: string | null\n\n  /**\n   * The target package type: list of `default`, `dmg`, `mas`, `mas-dev`, `pkg`, `7z`, `zip`, `tar.xz`, `tar.lz`, `tar.gz`, `tar.bz2`, `dir`. Defaults to `default` (`dmg` and `zip` for Squirrel.Mac). Note: Squirrel.Mac auto update mechanism requires both `dmg` and `zip` to be enabled, even when only `dmg` is used. Disabling `zip` will break auto update in `dmg` packages.\n   */\n  readonly target?: Array<MacOsTargetName | TargetConfiguration> | MacOsTargetName | TargetConfiguration | null\n\n  /**\n   * The name of certificate to use when signing. Consider using environment variables [CSC_LINK or CSC_NAME](/code-signing) instead of specifying this option.\n   * MAS installer identity is specified in the [mas](/configuration/mas).\n   */\n  readonly identity?: string | null\n\n  /**\n   * The path to application icon.\n   * @default build/icon.icns\n   */\n  readonly icon?: string | null\n\n  /**\n   * The path to entitlements file for signing the app. `build/entitlements.mac.plist` will be used if exists (it is a recommended way to set).\n   * MAS entitlements is specified in the [mas](/configuration/mas).\n   * See [this folder in osx-sign's repository](https://github.com/electron/osx-sign/tree/main/entitlements) for examples.\n   * Be aware that your app may crash if the right entitlements are not set like `com.apple.security.cs.allow-jit` for example on arm64 builds with Electron 20+.\n   * See [Signing and Notarizing macOS Builds from the Electron documentation](https://www.electronjs.org/docs/latest/tutorial/code-signing#signing--notarizing-macos-builds) for more information.\n   */\n  readonly entitlements?: string | null\n\n  /**\n   * The path to child entitlements which inherit the security settings for signing frameworks and bundles of a distribution. `build/entitlements.mac.inherit.plist` will be used if exists (it is a recommended way to set).\n   * See [this folder in osx-sign's repository](https://github.com/electron/osx-sign/tree/main/entitlements) for examples.\n   *\n   * This option only applies when signing with `entitlements` provided.\n   */\n  readonly entitlementsInherit?: string | null\n\n  /**\n   * Path to login helper entitlement file.\n   * When using App Sandbox, the the `com.apple.security.inherit` key that is normally in the inherited entitlements cannot be inherited since the login helper is a standalone executable.\n   * Defaults to the value provided for `entitlements`. This option only applies when signing with `entitlements` provided.\n   */\n  readonly entitlementsLoginHelper?: string | null\n\n  /**\n   * The path to the provisioning profile to use when signing, absolute or relative to the app root.\n   */\n  readonly provisioningProfile?: string | null\n\n  /**\n   * The `CFBundleVersion`. Do not use it unless [you need to](https://github.com/electron-userland/electron-builder/issues/565#issuecomment-230678643).\n   */\n  readonly bundleVersion?: string | null\n\n  /**\n   * The `CFBundleShortVersionString`. Do not use it unless you need to.\n   */\n  readonly bundleShortVersion?: string | null\n\n  /**\n   * Whether a dark mode is supported. If your app does have a dark mode, you can make your app follow the system-wide dark mode setting.\n   * @default false\n   */\n  readonly darkModeSupport?: boolean\n\n  /**\n   * The bundle identifier to use in the application helper's plist.\n   * @default ${appBundleIdentifier}.helper\n   */\n  readonly helperBundleId?: string | null\n\n  /**\n   * The bundle identifier to use in the Renderer helper's plist.\n   * @default ${appBundleIdentifier}.helper.Renderer\n   */\n  readonly helperRendererBundleId?: string | null\n\n  /**\n   * The bundle identifier to use in the Plugin helper's plist.\n   * @default ${appBundleIdentifier}.helper.Plugin\n   */\n  readonly helperPluginBundleId?: string | null\n\n  /**\n   * The bundle identifier to use in the GPU helper's plist.\n   * @default ${appBundleIdentifier}.helper.GPU\n   */\n  readonly helperGPUBundleId?: string | null\n\n  /**\n   * The bundle identifier to use in the EH helper's plist.\n   * @default ${appBundleIdentifier}.helper.EH\n   */\n  readonly helperEHBundleId?: string | null\n\n  /**\n   * The bundle identifier to use in the NP helper's plist.\n   * @default ${appBundleIdentifier}.helper.NP\n   */\n  readonly helperNPBundleId?: string | null\n\n  /**\n   * Whether to sign app for development or for distribution.\n   * @default distribution\n   */\n  readonly type?: \"distribution\" | \"development\" | null\n\n  /**\n   * The extra entries for `Info.plist`.\n   */\n  readonly extendInfo?: any\n\n  /**\n   * Paths of any extra binaries that need to be signed.\n   */\n  readonly binaries?: Array<string> | null\n\n  /**\n   * The minimum version of macOS required for the app to run. Corresponds to `LSMinimumSystemVersion`.\n   */\n  readonly minimumSystemVersion?: string | null\n\n  /**\n   * Path of [requirements file](https://developer.apple.com/library/mac/documentation/Security/Conceptual/CodeSigningGuide/RequirementLang/RequirementLang.html) used in signing. Not applicable for MAS.\n   */\n  readonly requirements?: string | null\n\n  /** @private */\n  readonly cscInstallerLink?: string | null\n  /** @private */\n  readonly cscInstallerKeyPassword?: string | null\n\n  /**\n   * Extra files to put in archive. Not applicable for `tar.*`.\n   */\n  readonly extraDistFiles?: Array<string> | string | null\n\n  /**\n   * Whether your app has to be signed with hardened runtime.\n   * @default true\n   */\n  readonly hardenedRuntime?: boolean\n\n  /**\n   * Whether to let @electron/osx-sign validate the signing or not.\n   * @default false\n   */\n  readonly gatekeeperAssess?: boolean\n\n  /**\n   * Whether to let @electron/osx-sign verify the contents or not.\n   * @default true\n   */\n  readonly strictVerify?: boolean\n\n  /**\n   * Whether to enable entitlements automation from @electron/osx-sign.\n   * @default true\n   */\n  readonly preAutoEntitlements?: boolean\n\n  /**\n   * Regex or an array of regex's that signal skipping signing a file.\n   */\n  readonly signIgnore?: Array<string> | string | null\n\n  /**\n   * The custom function (or path to file or module id) to sign an app bundle.\n   */\n  readonly sign?: CustomMacSign | string | null\n\n  /**\n   * Specify the URL of the timestamp authority server\n   */\n  readonly timestamp?: string | null\n\n  /**\n   * Whether to merge ASAR files for different architectures or not.\n   *\n   * This option has no effect unless building for \"universal\" arch.\n   * @default true\n   */\n  readonly mergeASARs?: boolean\n\n  /**\n   * Minimatch pattern of paths that are allowed to be present in one of the\n   * ASAR files, but not in the other.\n   *\n   * This option has no effect unless building for \"universal\" arch and applies\n   * only if `mergeASARs` is `true`.\n   */\n  readonly singleArchFiles?: string | null\n\n  /**\n   * Minimatch pattern of paths that are allowed to be x64 binaries in both\n   * ASAR files\n   *\n   * This option has no effect unless building for \"universal\" arch and applies\n   * only if `mergeASARs` is `true`.\n   */\n  readonly x64ArchFiles?: string | null\n\n  /**\n   * Options to use for @electron/notarize (ref: https://github.com/electron/notarize).\n   * Supports both `legacy` and `notarytool` notarization tools. Use `false` to explicitly disable\n   *\n   * Note: In order to activate the notarization step You MUST specify one of the following via environment variables:\n   * 1. `APPLE_API_KEY`, `APPLE_API_KEY_ID` and `APPLE_API_ISSUER`.\n   * 2. `APPLE_ID` and `APPLE_APP_SPECIFIC_PASSWORD`\n   * 3. `APPLE_KEYCHAIN` and `APPLE_KEYCHAIN_PROFILE`\n   *\n   * For security reasons it is recommended to use the first option (see https://github.com/electron-userland/electron-builder/issues/7859)\n   */\n  readonly notarize?: NotarizeLegacyOptions | NotarizeNotaryOptions | boolean | null\n}\n\nexport interface NotarizeLegacyOptions {\n  /**\n   * The app bundle identifier your Electron app is using. E.g. com.github.electron. Useful if notarization ID differs from app ID (unlikely).\n   * Only used by `legacy` notarization tool\n   */\n  readonly appBundleId?: string | null\n\n  /**\n   * Your Team Short Name. Only used by `legacy` notarization tool\n   */\n  readonly ascProvider?: string | null\n}\n\nexport interface NotarizeNotaryOptions {\n  /**\n   * The team ID you want to notarize under for when using `notarytool`\n   */\n  readonly teamId: string\n}\n\nexport interface DmgOptions extends TargetSpecificOptions {\n  /**\n   * The path to background image (default: `build/background.tiff` or `build/background.png` if exists). The resolution of this file determines the resolution of the installer window.\n   * If background is not specified, use `window.size`. Default locations expected background size to be 540x380.\n   * @see [DMG with Retina background support](http://stackoverflow.com/a/11204769/1910191).\n   */\n  background?: string | null\n\n  /**\n   * The background color (accepts css colors). Defaults to `#ffffff` (white) if no background image.\n   */\n  backgroundColor?: string | null\n\n  /**\n   * The path to DMG icon (volume icon), which will be shown when mounted, relative to the [build resources](/configuration/configuration#MetadataDirectories-buildResources) or to the project directory.\n   * Defaults to the application icon (`build/icon.icns`).\n   */\n  icon?: string | null\n\n  /**\n   * The size of all the icons inside the DMG.\n   * @default 80\n   */\n  readonly iconSize?: number | null\n\n  /**\n   * The size of all the icon texts inside the DMG.\n   * @default 12\n   */\n  readonly iconTextSize?: number | null\n\n  /**\n   * The title of the produced DMG, which will be shown when mounted (volume name).\n   *\n   * Macro `${productName}`, `${version}` and `${name}` are supported.\n   * @default ${productName} ${version}\n   */\n  readonly title?: string | null\n\n  /**\n   * The content — to customize icon locations. The x and y coordinates refer to the position of the **center** of the icon (at 1x scale), and do not take the label into account.\n   */\n  contents?: Array<DmgContent>\n\n  /**\n   * The disk image format. `ULFO` (lzfse-compressed image (OS X 10.11+ only)).\n   * @default UDZO\n   */\n  format?: \"UDRW\" | \"UDRO\" | \"UDCO\" | \"UDZO\" | \"UDBZ\" | \"ULFO\"\n\n  /**\n   * The DMG window position and size. With y co-ordinates running from bottom to top.\n   *\n   * The Finder makes sure that the window will be on the user’s display, so if you want your window at the top left of the display you could use `\"x\": 0, \"y\": 100000` as the x, y co-ordinates.\n   * It is not to be possible to position the window relative to the [top left](https://github.com/electron-userland/electron-builder/issues/3990#issuecomment-*********) or relative to the center of the user’s screen.\n   */\n  window?: DmgWindow\n\n  /**\n   * Whether to create internet-enabled disk image (when it is downloaded using a browser it will automatically decompress the image, put the application on the desktop, unmount and remove the disk image file).\n   * @default false\n   */\n  readonly internetEnabled?: boolean\n\n  /**\n   * Whether to sign the DMG or not. Signing is not required and will lead to unwanted errors in combination with notarization requirements.\n   * @default false\n   */\n  readonly sign?: boolean\n\n  /**\n   * @private\n   * @default true\n   */\n  writeUpdateInfo?: boolean\n}\n\nexport interface DmgWindow {\n  /**\n   * The X position relative to left of the screen.\n   * @default 400\n   */\n  x?: number\n\n  /**\n   * The Y position relative to bottom of the screen.\n   * @default 100\n   */\n  y?: number\n\n  /**\n   * The width. Defaults to background image width or 540.\n   */\n  width?: number\n\n  /**\n   * The height. Defaults to background image height or 380.\n   */\n  height?: number\n}\n\nexport interface DmgContent {\n  /**\n   * The device-independent pixel offset from the left of the window to the **center** of the icon.\n   */\n  x: number\n  /**\n   * The device-independent pixel offset from the top of the window to the **center** of the icon.\n   */\n  y: number\n  type?: \"link\" | \"file\" | \"dir\"\n\n  /**\n   * The name of the file within the DMG. Defaults to basename of `path`.\n   */\n  name?: string\n\n  /**\n   * The path of the file within the DMG.\n   */\n  path?: string\n}\n\nexport interface MasConfiguration extends MacConfiguration {\n  /**\n   * The path to entitlements file for signing the app. `build/entitlements.mas.plist` will be used if exists (it is a recommended way to set).\n   * See [this folder in osx-sign's repository](https://github.com/electron/osx-sign/tree/main/entitlements) for examples.\n   * Be aware that your app may crash if the right entitlements are not set like `com.apple.security.cs.allow-jit` for example on arm64 builds with Electron 20+.\n   * See [Signing and Notarizing macOS Builds from the Electron documentation](https://www.electronjs.org/docs/latest/tutorial/code-signing#signing--notarizing-macos-builds) for more information.\n   */\n  readonly entitlements?: string | null\n\n  /**\n   * The path to child entitlements which inherit the security settings for signing frameworks and bundles of a distribution. `build/entitlements.mas.inherit.plist` will be used if exists (it is a recommended way to set).\n   * See [this folder in osx-sign's repository](https://github.com/electron/osx-sign/tree/main/entitlements) for examples.\n   */\n  readonly entitlementsInherit?: string | null\n\n  /**\n   * Paths of any extra binaries that need to be signed.\n   */\n  readonly binaries?: Array<string> | null\n}\n"]}