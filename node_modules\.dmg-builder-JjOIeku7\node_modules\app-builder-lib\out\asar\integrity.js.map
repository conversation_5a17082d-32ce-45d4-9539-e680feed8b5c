{"version": 3, "file": "integrity.js", "sourceRoot": "", "sources": ["../../src/asar/integrity.ts"], "names": [], "mappings": ";;;AAAA,+CAA0C;AAC1C,mCAAmC;AACnC,2BAAqC;AACrC,0CAAqC;AACrC,6BAA4B;AAC5B,iCAAsD;AAgB/C,KAAK,UAAU,WAAW,CAAC,EAAE,aAAa,EAAE,qBAAqB,EAAwB;IAC9F,kCAAkC;IAClC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAA,kBAAO,EAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;IACtF,MAAM,SAAS,GAAG,MAAM,sBAAe,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;IAElG,MAAM,MAAM,GAAkB,EAAE,CAAA;IAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;IACnE,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAVD,kCAUC;AAED,KAAK,UAAU,UAAU,CAAC,IAAY;IACpC,MAAM,IAAI,GAAG,IAAA,mBAAU,EAAC,QAAQ,CAAC,CAAA;IACjC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,qBAAc,EAAC,IAAI,CAAC,CAAA;IAC7C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IACnB,OAAO;QACL,SAAS,EAAE,QAAQ;QACnB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;KACzB,CAAA;AACH,CAAC;AAED,SAAgB,QAAQ,CAAC,IAAY,EAAE,SAAS,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;IAChE,OAAO,IAAI,OAAO,CAAgB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACpD,MAAM,IAAI,GAAG,IAAA,mBAAU,EAAC,QAAQ,CAAC,CAAA;QAEjC,MAAM,MAAM,GAAG,IAAI,KAAK,EAAU,CAAA;QAElC,IAAI,UAAU,GAAG,CAAC,CAAA;QAClB,IAAI,SAAS,GAAG,IAAA,mBAAU,EAAC,QAAQ,CAAC,CAAA;QAEpC,SAAS,eAAe,CAAC,KAAa;YACpC,IAAI,GAAG,GAAG,CAAC,CAAA;YACX,OAAO,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;gBAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,UAAU,EAAE,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,CAAA;gBACnE,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,CAAA;gBAChD,GAAG,IAAI,MAAM,CAAA;gBACb,UAAU,IAAI,MAAM,CAAA;gBAEpB,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;oBAC7B,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;oBACpC,SAAS,GAAG,IAAA,mBAAU,EAAC,QAAQ,CAAC,CAAA;oBAChC,UAAU,GAAG,CAAC,CAAA;gBAChB,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAA,qBAAgB,EAAC,IAAI,CAAC;aACnB,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACf,4DAA4D;YAC5D,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;YAChC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;QACjB,CAAC,CAAC;aACD,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;aACnB,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YACd,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC;gBACrB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;YACtC,CAAC;YACD,OAAO,CAAC;gBACN,SAAS,EAAE,QAAQ;gBACnB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;gBACxB,SAAS;gBACT,MAAM;aACP,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACN,CAAC,CAAC,CAAA;AACJ,CAAC;AA5CD,4BA4CC;AAED,SAAgB,gBAAgB,CAAC,QAAyB,EAAE,SAAS,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;IACrF,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IACpC,MAAM,IAAI,GAAG,IAAA,mBAAU,EAAC,QAAQ,CAAC,CAAA;IACjC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IAEnB,MAAM,MAAM,GAAG,IAAI,KAAK,EAAU,CAAA;IAElC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,IAAI,SAAS,EAAE,CAAC;QACxD,MAAM,SAAS,GAAG,IAAA,mBAAU,EAAC,QAAQ,CAAC,CAAA;QACtC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,SAAS,CAAC,CAAC,CAAA;QACpD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;IACtC,CAAC;IAED,OAAO;QACL,SAAS,EAAE,QAAQ;QACnB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;QACxB,SAAS;QACT,MAAM;KACP,CAAA;AACH,CAAC;AAnBD,4CAmBC", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { createHash } from \"crypto\"\nimport { createReadStream } from \"fs\"\nimport { readdir } from \"fs/promises\"\nimport * as path from \"path\"\nimport { readAsarHeader, NodeIntegrity } from \"./asar\"\n\nexport interface AsarIntegrityOptions {\n  readonly resourcesPath: string\n  readonly resourcesRelativePath: string\n}\n\nexport interface HeaderHash {\n  algorithm: \"SHA256\"\n  hash: string\n}\n\nexport interface AsarIntegrity {\n  [key: string]: HeaderHash\n}\n\nexport async function computeData({ resourcesPath, resourcesRelativePath }: AsarIntegrityOptions): Promise<AsarIntegrity> {\n  // sort to produce constant result\n  const names = (await readdir(resourcesPath)).filter(it => it.endsWith(\".asar\")).sort()\n  const checksums = await BluebirdPromise.map(names, it => hashHeader(path.join(resourcesPath, it)))\n\n  const result: AsarIntegrity = {}\n  for (let i = 0; i < names.length; i++) {\n    result[path.join(resourcesRelativePath, names[i])] = checksums[i]\n  }\n  return result\n}\n\nasync function hashHeader(file: string): Promise<HeaderHash> {\n  const hash = createHash(\"sha256\")\n  const { header } = await readAsarHeader(file)\n  hash.update(header)\n  return {\n    algorithm: \"SHA256\",\n    hash: hash.digest(\"hex\"),\n  }\n}\n\nexport function hashFile(file: string, blockSize = 4 * 1024 * 1024): Promise<NodeIntegrity> {\n  return new Promise<NodeIntegrity>((resolve, reject) => {\n    const hash = createHash(\"sha256\")\n\n    const blocks = new Array<string>()\n\n    let blockBytes = 0\n    let blockHash = createHash(\"sha256\")\n\n    function updateBlockHash(chunk: Buffer) {\n      let off = 0\n      while (off < chunk.length) {\n        const toHash = Math.min(blockSize - blockBytes, chunk.length - off)\n        blockHash.update(chunk.slice(off, off + toHash))\n        off += toHash\n        blockBytes += toHash\n\n        if (blockBytes === blockSize) {\n          blocks.push(blockHash.digest(\"hex\"))\n          blockHash = createHash(\"sha256\")\n          blockBytes = 0\n        }\n      }\n    }\n\n    createReadStream(file)\n      .on(\"data\", it => {\n        // Note that `it` is a Buffer anyway so this cast is a no-op\n        updateBlockHash(Buffer.from(it))\n        hash.update(it)\n      })\n      .on(\"error\", reject)\n      .on(\"end\", () => {\n        if (blockBytes !== 0) {\n          blocks.push(blockHash.digest(\"hex\"))\n        }\n        resolve({\n          algorithm: \"SHA256\",\n          hash: hash.digest(\"hex\"),\n          blockSize,\n          blocks,\n        })\n      })\n  })\n}\n\nexport function hashFileContents(contents: Buffer | string, blockSize = 4 * 1024 * 1024): NodeIntegrity {\n  const buffer = Buffer.from(contents)\n  const hash = createHash(\"sha256\")\n  hash.update(buffer)\n\n  const blocks = new Array<string>()\n\n  for (let off = 0; off < buffer.length; off += blockSize) {\n    const blockHash = createHash(\"sha256\")\n    blockHash.update(buffer.slice(off, off + blockSize))\n    blocks.push(blockHash.digest(\"hex\"))\n  }\n\n  return {\n    algorithm: \"SHA256\",\n    hash: hash.digest(\"hex\"),\n    blockSize,\n    blocks,\n  }\n}\n"]}