{"version": 3, "file": "ElectronFramework.js", "sourceRoot": "", "sources": ["../../src/electron/ElectronFramework.ts"], "names": [], "mappings": ";;;AAAA,+CAA0C;AAC1C,+CAA8D;AAC9D,4CAA6G;AAC7G,uCAAoD;AACpD,6BAA4B;AAG5B,oCAA6C;AAG7C,qDAAqD;AACrD,+CAA4C;AAC5C,uDAA2F;AAC3F,kCAAiC;AACjC,iDAAyC;AAazC,SAAgB,kBAAkB,CAAC,IAAmB;;IACpD,OAAO;QACL,WAAW,EAAE,CAAA,MAAA,IAAI,CAAC,gBAAgB,0CAAE,WAAW,KAAI,UAAU;QAC7D,WAAW,EAAE,CAAA,MAAA,IAAI,CAAC,gBAAgB,0CAAE,WAAW,KAAI,UAAU;KAC9D,CAAA;AACH,CAAC;AALD,gDAKC;AA6BD,SAAS,kBAAkB,CAAC,IAAmB,EAAE,QAA8B,EAAE,IAAY,EAAE,eAAuB;IACpH,OAAO;QACL,QAAQ;QACR,IAAI;QACJ,OAAO,EAAE,eAAe;QACxB,GAAG,IAAI,CAAC,gBAAgB;KACzB,CAAA;AACH,CAAC;AAED,KAAK,UAAU,oBAAoB,CAAC,OAAoC;IACtE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAA;IACvC,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;IAC5D,IAAI,QAAQ,CAAC,QAAQ,KAAK,gBAAQ,CAAC,KAAK,EAAE,CAAC;QACzC,MAAM,aAAa,GAAG,QAAyB,CAAA;QAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC,cAAc,CAAC,CAAA;QACrE,MAAM,IAAA,iBAAM,EAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,gBAAgB,CAAC,WAAW,CAAC,EAAE,UAAU,CAAC,CAAA;IAC9E,CAAC;SAAM,IAAI,QAAQ,CAAC,QAAQ,KAAK,gBAAQ,CAAC,OAAO,EAAE,CAAC;QAClD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,eAAe,MAAM,CAAC,CAAA;QAClF,MAAM,IAAA,iBAAM,EAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,gBAAgB,CAAC,WAAW,MAAM,CAAC,EAAE,UAAU,CAAC,CAAA;IACvF,CAAC;SAAM,CAAC;QACN,MAAM,IAAA,0BAAY,EAAC,QAAuB,EAAE,SAAS,EAAE,OAAO,CAAC,aAAa,EAAG,OAAO,CAAC,YAAqC,KAAK,KAAK,CAAC,CAAA;IACzI,CAAC;IACD,MAAM,6BAA6B,CAAC,OAAO,CAAC,CAAA;AAC9C,CAAC;AAED,KAAK,UAAU,6BAA6B,CAAC,OAAoC;IAC/E,MAAM,EACJ,QAAQ,EAAE,EAAE,MAAM,EAAE,4BAA4B,EAAE,GACnD,GAAG,OAAO,CAAA;IACX,MAAM,eAAe,GAAG,IAAA,sBAAO,EAAC,4BAA4B,CAAC,iBAAiB,IAAI,MAAM,CAAC,iBAAiB,CAAC,CAAA;IAC3G,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;QAC5B,OAAM;IACR,CAAC;IAED,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAA;IACtD,uCAAuC;IACvC,MAAM,sBAAe,CAAC,GAAG,CACvB,IAAA,kBAAO,EAAC,GAAG,CAAC,EACZ,IAAI,CAAC,EAAE;QACL,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAChC,OAAM;QACR,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,CAAA;QACpE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxC,OAAO,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;QACtE,CAAC;QACD,OAAM;IACR,CAAC,EACD,gBAAW,CACZ,CAAA;IAED,SAAS,gBAAgB,CAAC,OAAoC;QAC5D,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAA;QACvC,IAAI,QAAQ,CAAC,QAAQ,KAAK,gBAAQ,CAAC,GAAG,EAAE,CAAC;YACvC,OAAO,EAAE,GAAG,EAAE,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAA;QAC5E,CAAC;aAAM,CAAC;YACN,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAA;QACtG,CAAC;IACH,CAAC;AACH,CAAC;AAED,MAAM,iBAAiB;IAUrB,YACW,IAAY,EACZ,OAAe,EACf,gBAAwB;QAFxB,SAAI,GAAJ,IAAI,CAAQ;QACZ,YAAO,GAAP,OAAO,CAAQ;QACf,qBAAgB,GAAhB,gBAAgB,CAAQ;QAZnC,qCAAqC;QAC5B,wBAAmB,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC7C,qCAAqC;QAC5B,uBAAkB,GAAG,eAAe,CAAA;QAC7C,qCAAqC;QAC5B,wBAAmB,GAAG,IAAI,CAAA;QACnC,qCAAqC;QAC5B,yBAAoB,GAAG,IAAI,CAAA;IAMjC,CAAC;IAEJ,cAAc,CAAC,QAAkB;QAC/B,IAAI,QAAQ,KAAK,gBAAQ,CAAC,KAAK,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAA,6BAAe,EAAC,OAAO,CAAC,EAAE,gBAAgB,CAAC,CAAA;QAC9D,CAAC;aAAM,CAAC;YACN,6CAA6C;YAC7C,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gCAAgC,CAAC,OAAgD;QACrF,MAAM,MAAM,CAAC,OAAO,EAAE,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAA;QAC3I,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;YACpD,MAAM,IAAA,sBAAY,EAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QAC3C,CAAC;IACH,CAAC;IAED,oBAAoB,CAAC,OAAoC;QACvD,OAAO,oBAAoB,CAAC,OAAO,CAAC,CAAA;IACtC,CAAC;CACF;AAEM,KAAK,UAAU,8BAA8B,CAAC,aAA4B,EAAE,QAAkB;IACnG,IAAI,OAAO,GAAG,aAAa,CAAC,eAAe,CAAA;IAC3C,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,qDAAqD;QACrD,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;YAChC,OAAO,GAAG,MAAM,IAAA,iDAA+B,EAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;YACpE,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAA;YACvE,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,MAAM,IAAA,wCAAsB,EAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;QAC7D,CAAC;QACD,aAAa,CAAC,eAAe,GAAG,OAAO,CAAA;IACzC,CAAC;IAED,MAAM,QAAQ,GAAG,kBAAkB,CAAC,aAAa,CAAC,CAAA;IAClD,OAAO,IAAI,iBAAiB,CAAC,QAAQ,CAAC,WAAW,EAAE,OAAO,EAAE,GAAG,QAAQ,CAAC,WAAW,MAAM,CAAC,CAAA;AAC5F,CAAC;AAjBD,wEAiBC;AAED,KAAK,UAAU,MAAM,CAAC,cAAuD,EAAE,OAAgC,EAAE,gBAAwB;IACvI,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,cAAc,CAAA;IAE5D,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAA;IACjD,IAAI,IAAI,GAA8B,OAAO,YAAY,KAAK,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,YAAY,CAAA;IACtH,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QACjB,MAAM,OAAO,GAAG,aAAa,OAAO,CAAC,OAAO,IAAI,YAAY,IAAI,OAAO,CAAC,IAAI,MAAM,CAAA;QAClF,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;QAC3F,IAAI,CAAC,MAAM,IAAA,eAAU,EAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;YACjE,kBAAG,CAAC,IAAI,CAAC,EAAE,YAAY,EAAE,OAAO,EAAE,EAAE,uBAAuB,CAAC,CAAA;YAC5D,OAAO,CAAC,KAAK,GAAG,YAAY,CAAA;YAC5B,IAAI,GAAG,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAED,IAAI,aAAa,GAAG,KAAK,CAAA;IACzB,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QACjB,MAAM,IAAA,gCAAiB,EAAC,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,UAAU,EAAE,SAAS,EAAE,oBAAoB,EAAE,gBAAgB,CAAC,CAAC,CAAA;IAC3J,CAAC;SAAM,CAAC;QACN,aAAa,GAAG,IAAI,CAAA;QACpB,MAAM,MAAM,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;QAC/C,MAAM,WAAW,GAAG,QAAQ,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAA;QACjE,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,kBAAkB,CAAC,CAAA;QACrD,MAAM,IAAA,mBAAQ,EAAC,SAAS,CAAC,CAAA;QACzB,MAAM,IAAA,YAAO,EAAC,MAAM,EAAE,WAAW,EAAE;YACjC,aAAa,EAAE,0BAAqB;SACrC,CAAC,CAAA;IACJ,CAAC;IAED,MAAM,kBAAkB,CAAC,cAAc,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAA;AAC3E,CAAC;AAED,SAAS,kBAAkB,CAAC,cAAuD,EAAE,gBAAwB,EAAE,aAAsB;IACnI,MAAM,GAAG,GAAG,cAAc,CAAC,SAAS,CAAA;IACpC,MAAM,KAAK,GAAG,cAAc,CAAC,QAAQ,CAAC,QAAQ,KAAK,gBAAQ,CAAC,GAAG,CAAA;IAC/D,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,gBAAgB,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,WAAW,CAAC,CAAA;IAErH,OAAO,OAAO,CAAC,GAAG,CAAC;QACjB,aAAa,CAAC,CAAC,CAAC,IAAA,mBAAc,EAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;QAChG,aAAa,CAAC,CAAC,CAAC,IAAA,mBAAc,EAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;QAC7E,KAAK;YACH,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;YACnB,CAAC,CAAC,IAAA,iBAAM,EAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,sBAAsB,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;gBACnF,YAAY;YACd,CAAC,CAAC;KACP,CAAC,CAAA;AACJ,CAAC", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { asArray, executeAppBuilder, log } from \"builder-util\"\nimport { CONCURRENCY, copyDir, DO_NOT_USE_HARD_LINKS, statOrNull, unlinkIfExists } from \"builder-util/out/fs\"\nimport { emptyDir, readdir, rename } from \"fs-extra\"\nimport * as path from \"path\"\nimport { Configuration } from \"../configuration\"\nimport { BeforeCopyExtraFilesOptions, Framework, PrepareApplicationStageDirectoryOptions } from \"../Framework\"\nimport { Packager, Platform } from \"../index\"\nimport { LinuxPackager } from \"../linuxPackager\"\nimport MacPackager from \"../macPackager\"\nimport { getTemplatePath } from \"../util/pathManager\"\nimport { createMacApp } from \"./electronMac\"\nimport { computeElectronVersion, getElectronVersionFromInstalled } from \"./electronVersion\"\nimport * as fs from \"fs/promises\"\nimport injectFFMPEG from \"./injectFFMPEG\"\n\nexport type ElectronPlatformName = \"darwin\" | \"linux\" | \"win32\" | \"mas\"\n\n/**\n * Electron distributables branding options.\n * @see [Electron BRANDING.json](https://github.com/electron/electron/blob/master/shell/app/BRANDING.json).\n */\nexport interface ElectronBrandingOptions {\n  projectName?: string\n  productName?: string\n}\n\nexport function createBrandingOpts(opts: Configuration): Required<ElectronBrandingOptions> {\n  return {\n    projectName: opts.electronBranding?.projectName || \"electron\",\n    productName: opts.electronBranding?.productName || \"Electron\",\n  }\n}\n\nexport interface ElectronDownloadOptions {\n  // https://github.com/electron-userland/electron-builder/issues/3077\n  // must be optional\n  version?: string\n\n  /**\n   * The [cache location](https://github.com/electron-userland/electron-download#cache-location).\n   */\n  cache?: string | null\n\n  /**\n   * The mirror.\n   */\n  mirror?: string | null\n\n  /** @private */\n  customDir?: string | null\n  /** @private */\n  customFilename?: string | null\n\n  strictSSL?: boolean\n  isVerifyChecksum?: boolean\n\n  platform?: ElectronPlatformName\n  arch?: string\n}\n\nfunction createDownloadOpts(opts: Configuration, platform: ElectronPlatformName, arch: string, electronVersion: string): ElectronDownloadOptions {\n  return {\n    platform,\n    arch,\n    version: electronVersion,\n    ...opts.electronDownload,\n  }\n}\n\nasync function beforeCopyExtraFiles(options: BeforeCopyExtraFilesOptions) {\n  const { appOutDir, packager } = options\n  const electronBranding = createBrandingOpts(packager.config)\n  if (packager.platform === Platform.LINUX) {\n    const linuxPackager = packager as LinuxPackager\n    const executable = path.join(appOutDir, linuxPackager.executableName)\n    await rename(path.join(appOutDir, electronBranding.projectName), executable)\n  } else if (packager.platform === Platform.WINDOWS) {\n    const executable = path.join(appOutDir, `${packager.appInfo.productFilename}.exe`)\n    await rename(path.join(appOutDir, `${electronBranding.projectName}.exe`), executable)\n  } else {\n    await createMacApp(packager as MacPackager, appOutDir, options.asarIntegrity, (options.platformName as ElectronPlatformName) === \"mas\")\n  }\n  await removeUnusedLanguagesIfNeeded(options)\n}\n\nasync function removeUnusedLanguagesIfNeeded(options: BeforeCopyExtraFilesOptions) {\n  const {\n    packager: { config, platformSpecificBuildOptions },\n  } = options\n  const wantedLanguages = asArray(platformSpecificBuildOptions.electronLanguages || config.electronLanguages)\n  if (!wantedLanguages.length) {\n    return\n  }\n\n  const { dir, langFileExt } = getLocalesConfig(options)\n  // noinspection SpellCheckingInspection\n  await BluebirdPromise.map(\n    readdir(dir),\n    file => {\n      if (!file.endsWith(langFileExt)) {\n        return\n      }\n\n      const language = file.substring(0, file.length - langFileExt.length)\n      if (!wantedLanguages.includes(language)) {\n        return fs.rm(path.join(dir, file), { recursive: true, force: true })\n      }\n      return\n    },\n    CONCURRENCY\n  )\n\n  function getLocalesConfig(options: BeforeCopyExtraFilesOptions) {\n    const { appOutDir, packager } = options\n    if (packager.platform === Platform.MAC) {\n      return { dir: packager.getResourcesDir(appOutDir), langFileExt: \".lproj\" }\n    } else {\n      return { dir: path.join(packager.getResourcesDir(appOutDir), \"..\", \"locales\"), langFileExt: \".pak\" }\n    }\n  }\n}\n\nclass ElectronFramework implements Framework {\n  // noinspection JSUnusedGlobalSymbols\n  readonly macOsDefaultTargets = [\"zip\", \"dmg\"]\n  // noinspection JSUnusedGlobalSymbols\n  readonly defaultAppIdPrefix = \"com.electron.\"\n  // noinspection JSUnusedGlobalSymbols\n  readonly isCopyElevateHelper = true\n  // noinspection JSUnusedGlobalSymbols\n  readonly isNpmRebuildRequired = true\n\n  constructor(\n    readonly name: string,\n    readonly version: string,\n    readonly distMacOsAppName: string\n  ) {}\n\n  getDefaultIcon(platform: Platform) {\n    if (platform === Platform.LINUX) {\n      return path.join(getTemplatePath(\"icons\"), \"electron-linux\")\n    } else {\n      // default icon is embedded into app skeleton\n      return null\n    }\n  }\n\n  async prepareApplicationStageDirectory(options: PrepareApplicationStageDirectoryOptions) {\n    await unpack(options, createDownloadOpts(options.packager.config, options.platformName, options.arch, this.version), this.distMacOsAppName)\n    if (options.packager.config.downloadAlternateFFmpeg) {\n      await injectFFMPEG(options, this.version)\n    }\n  }\n\n  beforeCopyExtraFiles(options: BeforeCopyExtraFilesOptions) {\n    return beforeCopyExtraFiles(options)\n  }\n}\n\nexport async function createElectronFrameworkSupport(configuration: Configuration, packager: Packager): Promise<Framework> {\n  let version = configuration.electronVersion\n  if (version == null) {\n    // for prepacked app asar no dev deps in the app.asar\n    if (packager.isPrepackedAppAsar) {\n      version = await getElectronVersionFromInstalled(packager.projectDir)\n      if (version == null) {\n        throw new Error(`Cannot compute electron version for prepacked asar`)\n      }\n    } else {\n      version = await computeElectronVersion(packager.projectDir)\n    }\n    configuration.electronVersion = version\n  }\n\n  const branding = createBrandingOpts(configuration)\n  return new ElectronFramework(branding.projectName, version, `${branding.productName}.app`)\n}\n\nasync function unpack(prepareOptions: PrepareApplicationStageDirectoryOptions, options: ElectronDownloadOptions, distMacOsAppName: string) {\n  const { packager, appOutDir, platformName } = prepareOptions\n\n  const electronDist = packager.config.electronDist\n  let dist: string | undefined | null = typeof electronDist === \"function\" ? electronDist(prepareOptions) : electronDist\n  if (dist != null) {\n    const zipFile = `electron-v${options.version}-${platformName}-${options.arch}.zip`\n    const resolvedDist = path.isAbsolute(dist) ? dist : path.resolve(packager.projectDir, dist)\n    if ((await statOrNull(path.join(resolvedDist, zipFile))) != null) {\n      log.info({ resolvedDist, zipFile }, \"resolved electronDist\")\n      options.cache = resolvedDist\n      dist = null\n    }\n  }\n\n  let isFullCleanup = false\n  if (dist == null) {\n    await executeAppBuilder([\"unpack-electron\", \"--configuration\", JSON.stringify([options]), \"--output\", appOutDir, \"--distMacOsAppName\", distMacOsAppName])\n  } else {\n    isFullCleanup = true\n    const source = packager.getElectronSrcDir(dist)\n    const destination = packager.getElectronDestinationDir(appOutDir)\n    log.info({ source, destination }, \"copying Electron\")\n    await emptyDir(appOutDir)\n    await copyDir(source, destination, {\n      isUseHardLink: DO_NOT_USE_HARD_LINKS,\n    })\n  }\n\n  await cleanupAfterUnpack(prepareOptions, distMacOsAppName, isFullCleanup)\n}\n\nfunction cleanupAfterUnpack(prepareOptions: PrepareApplicationStageDirectoryOptions, distMacOsAppName: string, isFullCleanup: boolean) {\n  const out = prepareOptions.appOutDir\n  const isMac = prepareOptions.packager.platform === Platform.MAC\n  const resourcesPath = isMac ? path.join(out, distMacOsAppName, \"Contents\", \"Resources\") : path.join(out, \"resources\")\n\n  return Promise.all([\n    isFullCleanup ? unlinkIfExists(path.join(resourcesPath, \"default_app.asar\")) : Promise.resolve(),\n    isFullCleanup ? unlinkIfExists(path.join(out, \"version\")) : Promise.resolve(),\n    isMac\n      ? Promise.resolve()\n      : rename(path.join(out, \"LICENSE\"), path.join(out, \"LICENSE.electron.txt\")).catch(() => {\n          /* ignore */\n        }),\n  ])\n}\n"]}