{"version": 3, "file": "LibUiFramework.js", "sourceRoot": "", "sources": ["../../src/frameworks/LibUiFramework.ts"], "names": [], "mappings": ";;;AAAA,uCAAmC;AACnC,0CAA6D;AAC7D,6BAA4B;AAC5B,+CAAgD;AAEhD,kCAAkC;AAIlC,mDAAkE;AAElE,MAAa,cAAc;IAazB,YACW,OAAe,EACf,gBAAwB,EACd,aAAsB;QAFhC,YAAO,GAAP,OAAO,CAAQ;QACf,qBAAgB,GAAhB,gBAAgB,CAAQ;QACd,kBAAa,GAAb,aAAa,CAAS;QAflC,SAAI,GAAW,OAAO,CAAA;QAC/B,qCAAqC;QAC5B,wBAAmB,GAAG,CAAC,KAAK,CAAC,CAAA;QAE7B,uBAAkB,GAAW,YAAY,CAAA;QAElD,qCAAqC;QAC5B,wBAAmB,GAAG,KAAK,CAAA;QAEpC,qCAAqC;QAC5B,yBAAoB,GAAG,KAAK,CAAA;IAMlC,CAAC;IAEJ,KAAK,CAAC,gCAAgC,CAAC,OAAgD;QACrF,MAAM,IAAA,mBAAQ,EAAC,OAAO,CAAC,SAAS,CAAC,CAAA;QAEjC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;QACjC,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAA;QAElC,IAAI,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5C,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;YACnC,MAAM,IAAA,gCAAiB,EAAC;gBACtB,eAAe;gBACf,gBAAgB;gBAChB,IAAI,CAAC,OAAO;gBACZ,iBAAiB;gBACjB,YAAY;gBACZ,QAAQ,CAAC,QAAQ;gBACjB,QAAQ;gBACR,OAAO,CAAC,IAAI;gBACZ,SAAS;gBACT,SAAS;gBACT,cAAc;gBACd,GAAG,QAAQ,CAAC,OAAO,CAAC,eAAe,GAAG,QAAQ,KAAK,eAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;aACpF,CAAC,CAAA;YACF,OAAM;QACR,CAAC;QAED,IAAI,QAAQ,KAAK,eAAQ,CAAC,GAAG,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC,qCAAqC,CAAC,QAAuB,EAAE,OAAO,CAAC,CAAA;QACpF,CAAC;aAAM,IAAI,QAAQ,KAAK,eAAQ,CAAC,KAAK,EAAE,CAAC;YACvC,MAAM,IAAI,CAAC,qCAAqC,CAAC,OAAO,CAAC,CAAA;QAC3D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qCAAqC,CAAC,QAAqB,EAAE,OAAgD;QACzH,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAA;QACtF,MAAM,IAAA,gBAAK,EAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;QACxE,MAAM,IAAA,gBAAK,EAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;QACpE,MAAM,IAAA,gCAAiB,EAAC,CAAC,eAAe,EAAE,gBAAgB,EAAE,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC,CAAA;QAEjJ,MAAM,QAAQ,GAAQ;YACpB,6DAA6D;YAC7D,uBAAuB,EAAE,IAAI;SAC9B,CAAA;QACD,MAAM,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAA;QACxD,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,IAAA,0CAA6B,EAAC,CAAC,cAAc,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC;YACxG,mBAAmB,CACjB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,QAAQ,CAAC,kBAAkB,CAAC,EAC/D;;uCAE+B,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU;GACrF,CACI;SACF,CAAC,CAAA;IACJ,CAAC;IAEO,KAAK,CAAC,qCAAqC,CAAC,OAAgD;QAClG,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;QACnC,MAAM,IAAA,gCAAiB,EAAC,CAAC,eAAe,EAAE,gBAAgB,EAAE,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAA;QAC/I,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAG,OAAO,CAAC,QAA0B,CAAC,cAAc,CAAC,CAAA;QACzF,MAAM,mBAAmB,CACvB,QAAQ,EACR;;0BAEoB,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU;GACxE,CACE,CAAA;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,OAAyB;QACvC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;QACjC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtD,OAAM;QACR,CAAC;QAED,4CAA4C;QAC5C,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,CAAA;QAC1D,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,OAAM;QACR,CAAC;QAED,MAAM,IAAA,iBAAM,EAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC,CAAA;IAC7G,CAAC;IAED,WAAW,CAAC,QAAkB;QAC5B,OAAO,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAA;IACnE,CAAC;IAEO,wBAAwB,CAAC,QAAkB;QACjD,OAAO,QAAQ,KAAK,eAAQ,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,QAAQ,KAAK,eAAQ,CAAC,KAAK,CAAC,CAAA;IAC7F,CAAC;IAED,uBAAuB,CAAC,QAAkB;QACxC,mBAAmB;QACnB,OAAO,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IACxE,CAAC;CACF;AAlHD,wCAkHC;AAED,KAAK,UAAU,mBAAmB,CAAC,IAAY,EAAE,OAAe;IAC9D,MAAM,IAAA,oBAAS,EAAC,IAAI,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAA;IAC/C,MAAM,IAAA,gBAAK,EAAC,IAAI,EAAE,KAAK,CAAC,CAAA;AAC1B,CAAC", "sourcesContent": ["import { emptyDir } from \"fs-extra\"\nimport { mkdir, chmod, rename, writeFile } from \"fs/promises\"\nimport * as path from \"path\"\nimport { executeAppBuilder } from \"builder-util\"\nimport { AfterPackContext } from \"../configuration\"\nimport { Platform } from \"../core\"\nimport { Framework, PrepareApplicationStageDirectoryOptions } from \"../Framework\"\nimport { LinuxPackager } from \"../linuxPackager\"\nimport MacPackager from \"../macPackager\"\nimport { executeAppBuilderAndWriteJson } from \"../util/appBuilder\"\n\nexport class LibUiFramework implements Framework {\n  readonly name: string = \"libui\"\n  // noinspection JSUnusedGlobalSymbols\n  readonly macOsDefaultTargets = [\"dmg\"]\n\n  readonly defaultAppIdPrefix: string = \"com.libui.\"\n\n  // noinspection JSUnusedGlobalSymbols\n  readonly isCopyElevateHelper = false\n\n  // noinspection JSUnusedGlobalSymbols\n  readonly isNpmRebuildRequired = false\n\n  constructor(\n    readonly version: string,\n    readonly distMacOsAppName: string,\n    protected readonly isUseLaunchUi: boolean\n  ) {}\n\n  async prepareApplicationStageDirectory(options: PrepareApplicationStageDirectoryOptions) {\n    await emptyDir(options.appOutDir)\n\n    const packager = options.packager\n    const platform = packager.platform\n\n    if (this.isUseLaunchUiForPlatform(platform)) {\n      const appOutDir = options.appOutDir\n      await executeAppBuilder([\n        \"proton-native\",\n        \"--node-version\",\n        this.version,\n        \"--use-launch-ui\",\n        \"--platform\",\n        platform.nodeName,\n        \"--arch\",\n        options.arch,\n        \"--stage\",\n        appOutDir,\n        \"--executable\",\n        `${packager.appInfo.productFilename}${platform === Platform.WINDOWS ? \".exe\" : \"\"}`,\n      ])\n      return\n    }\n\n    if (platform === Platform.MAC) {\n      await this.prepareMacosApplicationStageDirectory(packager as MacPackager, options)\n    } else if (platform === Platform.LINUX) {\n      await this.prepareLinuxApplicationStageDirectory(options)\n    }\n  }\n\n  private async prepareMacosApplicationStageDirectory(packager: MacPackager, options: PrepareApplicationStageDirectoryOptions) {\n    const appContentsDir = path.join(options.appOutDir, this.distMacOsAppName, \"Contents\")\n    await mkdir(path.join(appContentsDir, \"Resources\"), { recursive: true })\n    await mkdir(path.join(appContentsDir, \"MacOS\"), { recursive: true })\n    await executeAppBuilder([\"proton-native\", \"--node-version\", this.version, \"--platform\", \"darwin\", \"--stage\", path.join(appContentsDir, \"MacOS\")])\n\n    const appPlist: any = {\n      // https://github.com/albe-rosado/create-proton-app/issues/13\n      NSHighResolutionCapable: true,\n    }\n    await packager.applyCommonInfo(appPlist, appContentsDir)\n    await Promise.all([\n      executeAppBuilderAndWriteJson([\"encode-plist\"], { [path.join(appContentsDir, \"Info.plist\")]: appPlist }),\n      writeExecutableMain(\n        path.join(appContentsDir, \"MacOS\", appPlist.CFBundleExecutable),\n        `#!/bin/sh\n  DIR=$(dirname \"$0\")\n  \"$DIR/node\" \"$DIR/../Resources/app/${options.packager.info.metadata.main || \"index.js\"}\"\n  `\n      ),\n    ])\n  }\n\n  private async prepareLinuxApplicationStageDirectory(options: PrepareApplicationStageDirectoryOptions) {\n    const appOutDir = options.appOutDir\n    await executeAppBuilder([\"proton-native\", \"--node-version\", this.version, \"--platform\", \"linux\", \"--arch\", options.arch, \"--stage\", appOutDir])\n    const mainPath = path.join(appOutDir, (options.packager as LinuxPackager).executableName)\n    await writeExecutableMain(\n      mainPath,\n      `#!/bin/sh\n  DIR=$(dirname \"$0\")\n  \"$DIR/node\" \"$DIR/app/${options.packager.info.metadata.main || \"index.js\"}\"\n  `\n    )\n  }\n\n  async afterPack(context: AfterPackContext) {\n    const packager = context.packager\n    if (!this.isUseLaunchUiForPlatform(packager.platform)) {\n      return\n    }\n\n    // LaunchUI requires main.js, rename if need\n    const userMain = packager.info.metadata.main || \"index.js\"\n    if (userMain === \"main.js\") {\n      return\n    }\n\n    await rename(path.join(context.appOutDir, \"app\", userMain), path.join(context.appOutDir, \"app\", \"main.js\"))\n  }\n\n  getMainFile(platform: Platform): string | null {\n    return this.isUseLaunchUiForPlatform(platform) ? \"main.js\" : null\n  }\n\n  private isUseLaunchUiForPlatform(platform: Platform) {\n    return platform === Platform.WINDOWS || (this.isUseLaunchUi && platform === Platform.LINUX)\n  }\n\n  getExcludedDependencies(platform: Platform): Array<string> | null {\n    // part of launchui\n    return this.isUseLaunchUiForPlatform(platform) ? [\"libui-node\"] : null\n  }\n}\n\nasync function writeExecutableMain(file: string, content: string) {\n  await writeFile(file, content, { mode: 0o755 })\n  await chmod(file, 0o755)\n}\n"]}