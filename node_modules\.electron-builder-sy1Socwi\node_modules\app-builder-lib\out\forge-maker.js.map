{"version": 3, "file": "forge-maker.js", "sourceRoot": "", "sources": ["../src/forge-maker.ts"], "names": [], "mappings": ";;;AAAA,6BAA4B;AAC5B,mCAA+B;AAO/B,SAAgB,UAAU,CAAC,YAA0B,EAAE,OAAwB;IAC7E,MAAM,MAAM,GAAG,YAAY,CAAC,GAAG,CAAA;IAC/B,OAAO,IAAA,aAAK,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,MAAM,EAAE;YACN,WAAW,EAAE;gBACX,4FAA4F;gBAC5F,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC;aAC3C;SACF;QACD,GAAG,OAAO;KACX,CAAC,CAAA;AACJ,CAAC;AAZD,gCAYC", "sourcesContent": ["import * as path from \"path\"\nimport { build } from \"./index\"\nimport { PackagerOptions } from \"./packagerApi\"\n\nexport interface ForgeOptions {\n  readonly dir: string\n}\n\nexport function buildForge(forgeOptions: ForgeOptions, options: PackagerOptions) {\n  const appDir = forgeOptions.dir\n  return build({\n    prepackaged: appDir,\n    config: {\n      directories: {\n        // https://github.com/electron-userland/electron-forge/blob/master/src/makers/generic/zip.js\n        output: path.resolve(appDir, \"..\", \"make\"),\n      },\n    },\n    ...options,\n  })\n}\n"]}