{"version": 3, "file": "bin.js", "sourceRoot": "", "sources": ["../src/bin.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA,+BAA+B;AAC/B,qCAAqC;AACrC,6BAA6B;AAE7B,wBAAkD;AAClD,qCAAkE;AAClE,2CAAwC;AAYxC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAE7B,MAAM,YAAY,GAAG,yCAAyC,CAAC;AAC/D,MAAM,aAAa,GAAG,0DAA0D,CAAC;AAEjF,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,OAAO,EAAE;IACvC,OAAO,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;IACtD,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC3B,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC5B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACjB;AAED,SAAS,cAAc,CAAC,KAAgB;IACtC,QAAQ,KAAK,EAAE;QACb,KAAK,qBAAS,CAAC,MAAM;YACnB,OAAO,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAChC,KAAK,qBAAS,CAAC,OAAO;YACpB,OAAO,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC/B,KAAK,qBAAS,CAAC,OAAO;YACpB,OAAO,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACnC,KAAK,qBAAS,CAAC,OAAO;YACpB,OAAO,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;KACpD;AACH,CAAC;AAED,IAAI,IAAI,KAAK,MAAM,EAAE;IACnB,MAAM,IAAI,GAAG,QAAQ,CAAkB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;QAC5D,MAAM,EAAE,CAAC,KAAK,CAAC;QACf,OAAO,EAAE,CAAC,MAAM,CAAC;KAClB,CAAC,CAAC;IAEH,IAAI,IAAI,CAAC,IAAI,EAAE;QACb,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;IAED,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;QACb,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAC5C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;IAED,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEnE,IAAA,qBAAkB,EAAC,IAAI,CAAC,GAAG,CAAC;SACzB,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;QACf,MAAM,EAAE,OAAO,EAAE,yBAAyB,EAAE,uBAAuB,KAAc,MAAM,EAAf,IAAI,UAAK,MAAM,EAAjF,mEAAwE,CAAS,CAAC;QACxF,OAAO,CAAC,GAAG,CAAC,iBAAiB,KAAK,CAAC,IAAI,CAAC,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;QAE1D,QAAQ,MAAM,CAAC,OAAO,EAAE;YACtB,KAAK,oBAAW,CAAC,EAAE;gBACjB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACnC,OAAO,CAAC,GAAG,CACT,KAAK,KAAK,CAAC,MAAM,CAAC,sBAAa,CAAC,GAAU,CAAC,CAAC,OAAO,cAAc,CAC/D,IAAI,CAAE,GAAgC,CAAE,CACzC,EAAE,CACJ,CAAC;iBACH;gBACD,MAAM;SACT;IACH,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;QACb,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACnB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;CACN;KAAM;IACL,MAAM,IAAI,GAAG,QAAQ,CAAmB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;QAC7D,MAAM,EAAE,CAAC,KAAK,CAAC;QACf,OAAO,EAAE,CAAC,MAAM,CAAC;KAClB,CAAC,CAAC;IAEH,IAAI,IAAI,CAAC,IAAI,EAAE;QACb,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC3B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;IAED,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;QACb,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAC5C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;IAED,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEnE,IAAA,qBAAkB,EAAC,IAAI,CAAC,GAAG,CAAC;SACzB,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;QACf,MAAM,EAAE,OAAO,EAAE,yBAAyB,KAAc,MAAM,EAAf,IAAI,UAAK,MAAM,EAAxD,wCAA+C,CAAS,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,iBAAiB,KAAK,CAAC,IAAI,CAAC,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;QAE1D,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;QAC9B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;YAC9B,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACxC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;gBAClB,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;gBACxC,OAAO,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;gBACvD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACjB;YAED,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE;gBACrC,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;gBAC5D,OAAO,CAAC,KAAK,CACX,iCAAiC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CACrF,CAAC;gBACF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACjB;YAED,QAAQ,MAAM,CAAC,OAAO,EAAE;gBACtB,KAAK,oBAAW,CAAC,EAAE;oBACjB,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,sBAAa,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrF,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;wBACjC,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;wBACtD,OAAO,CAAC,KAAK,CACX,4BAA4B,EAC5B,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAC7C,CAAC;wBACF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;qBACjB;oBACD,MAAM,YAAY,GAAI,MAAc,CAAC,sBAAa,CAAC,GAAU,CAAC,CAAE,CAAC;oBACjE,MAAM,QAAQ,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,qBAAS,CAAC,MAAM,CAAC,CAAC,CAAC,qBAAS,CAAC,OAAO,CAAC;oBACvE,IAAI,YAAY,KAAK,QAAQ,EAAE;wBAC7B,OAAO,CAAC,GAAG,CACT,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,cAAc,CACjD,YAAY,CACb,0BAA0B,CAC5B,CAAC;qBACH;yBAAM;wBACL,OAAO,CAAC,GAAG,CACT,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,cAAc,CACzC,YAAY,CACb,oBAAoB,cAAc,CAAC,QAAQ,CAAC,EAAE,CAChD,CAAC;qBACH;oBACA,MAAc,CAAC,sBAAa,CAAC,GAAU,CAAC,CAAC,GAAG,QAAQ,CAAC;oBACtD,MAAM;aACT;SACF;QAED,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAI,CAAC,CAAC,CAAC,CAAC;QAErE,SAAS,WAAW,CAAC,MAA6B;YAChD,MAAM,EAAE,OAAO,EAAE,yBAAyB,KAAc,MAAM,EAAf,IAAI,UAAK,MAAM,EAAxD,wCAA+C,CAAS,CAAC;YAC/D,MAAM,UAAU,GAAwB;gBACtC,OAAO;gBACP,yBAAyB;aAC1B,CAAC;YAEF,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAClC,UAAkB,CAAC,GAAG,CAAC,GAAI,IAAY,CAAC,GAAG,CAAC,KAAK,qBAAS,CAAC,MAAM,CAAC;aACpE;YAED,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,OAAO,IAAA,YAAS,EAAC,IAAI,CAAC,GAAI,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC;SACD,IAAI,CAAC,GAAG,EAAE;QACT,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;QACb,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACnB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;CACN"}