{"version": 3, "file": "macPackager.js", "sourceRoot": "", "sources": ["../src/macPackager.ts"], "names": [], "mappings": ";;AAAA,+CAA0C;AAC1C,+CAA2H;AAC3H,iDAA8C;AAE9C,0CAA4C;AAC5C,uCAA+B;AAC/B,6BAA4B;AAC5B,4CAA0E;AAC1E,sDAA2D;AAC3D,uCAAmC;AACnC,wDAAsJ;AACtJ,iCAAqD;AAIrD,yDAAqF;AACrF,2DAAuD;AACvD,uCAAkE;AAClE,2DAAwE;AACxE,sDAAuD;AACvD,oDAAoD;AACpD,kCAAiC;AACjC,iDAA8D;AAY9D,MAAqB,WAAY,SAAQ,mCAAkC;IAyBzE,YAAY,IAAc;QACxB,KAAK,CAAC,IAAI,EAAE,eAAQ,CAAC,GAAG,CAAC,CAAA;QAzBlB,oBAAe,GAAG,IAAI,eAAI,CAAkB,GAAG,EAAE;YACxD,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;YACjC,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACrD,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,IAAI,EAAE,CAAC,CAAA;YAC5E,CAAC;YAED,OAAO,IAAA,4BAAc,EAAC;gBACpB,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc;gBAChC,OAAO;gBACP,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;gBACrC,QAAQ,EAAE,IAAA,gCAAa,EAAC,IAAI,CAAC,4BAA4B,CAAC,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;gBAC3G,eAAe,EAAE,IAAA,gCAAa,EAAC,IAAI,CAAC,4BAA4B,CAAC,uBAAuB,EAAE,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;gBACjI,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBACf,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,CAAA;gBACxC,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;oBACzB,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,CAAC,IAAA,4BAAc,EAAC,YAAY,CAAC,CAAC,CAAA;gBACpE,CAAC;gBACD,OAAO,MAAM,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEM,cAAS,GAAG,IAAI,eAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAA;IAIjE,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAA;IAChD,CAAC;IAED,6DAA6D;IACnD,cAAc,CAAC,OAAgB;QACvC,kEAAkE;QAClE,OAAO,IAAI,iBAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,4BAA4B,CAAC,aAAa,EAAE,IAAI,CAAC,4BAA4B,EAAE,IAAI,CAAC,CAAA;IACzH,CAAC;IAED,KAAK,CAAC,WAAW;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAA;IAC7B,CAAC;IAED,aAAa,CAAC,OAAsB,EAAE,MAAmE;QACvG,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE,CAAC;YAC3B,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,iBAAU;oBACb,MAAK;gBAEP,KAAK,KAAK,CAAC,CAAC,CAAC;oBACX,8DAA8D;oBAC9D,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,aAAa,CAAC,CAAA;oBAC5C,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,IAAI,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAA;oBACnD,MAAK;gBACP,CAAC;gBAED,KAAK,KAAK;oBACR,oEAAoE;oBACpE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,IAAI,6BAAa,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;oBACnE,MAAK;gBAEP,KAAK,KAAK;oBACR,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,IAAI,eAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAA;oBACnD,MAAK;gBAEP;oBACE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,0BAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAA,kCAAkB,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAA;oBAC9H,MAAK;YACT,CAAC;QACH,CAAC;IACH,CAAC;IAES,KAAK,CAAC,MAAM,CACpB,MAAc,EACd,SAAiB,EACjB,YAAkC,EAClC,IAAU,EACV,4BAA8C,EAC9C,OAAsB;;QAEtB,QAAQ,IAAI,EAAE,CAAC;YACb,OAAO,CAAC,CAAC,CAAC;gBACR,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,IAAI,EAAE,4BAA4B,EAAE,OAAO,CAAC,CAAA;YACnG,CAAC;YACD,KAAK,mBAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBACpB,MAAM,UAAU,GAAG,CAAC,IAAU,EAAE,EAAE,CAAC,GAAG,SAAS,IAAI,mBAAI,CAAC,IAAI,CAAC,OAAO,CAAA;gBAEpE,MAAM,OAAO,GAAG,mBAAI,CAAC,GAAG,CAAA;gBACxB,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,CAAA;gBACxC,MAAM,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;gBACnH,MAAM,SAAS,GAAG,mBAAI,CAAC,KAAK,CAAA;gBAC5B,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC,CAAA;gBAC7C,MAAM,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,eAAe,EAAE,YAAY,EAAE,SAAS,EAAE,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;gBACxH,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAA;gBACrC,kBAAG,CAAC,IAAI,CACN;oBACE,QAAQ,EAAE,YAAY;oBACtB,IAAI,EAAE,mBAAI,CAAC,IAAI,CAAC;oBAChB,CAAC,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC,EAAE,SAAS,CAAC,OAAO;oBACxC,SAAS,EAAE,kBAAG,CAAC,QAAQ,CAAC,SAAS,CAAC;iBACnC,EACD,WAAW,CACZ,CAAA;gBACD,MAAM,OAAO,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,MAAM,CAAA;gBACrD,MAAM,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAA;gBAC3D,MAAM,gBAAgB,CAAC;oBACrB,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC;oBAC5C,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC;oBACjD,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC;oBACzC,KAAK,EAAE,IAAI;oBACX,UAAU,EAAE,MAAA,4BAA4B,CAAC,UAAU,mCAAI,IAAI;oBAC3D,eAAe,EAAE,4BAA4B,CAAC,eAAe;oBAC7D,YAAY,EAAE,4BAA4B,CAAC,YAAY;iBACxD,CAAC,CAAA;gBACF,MAAM,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;gBAC3D,MAAM,EAAE,CAAC,EAAE,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;gBAE9D,oGAAoG;gBACpG,MAAM,WAAW,GAAqB;oBACpC,SAAS;oBACT,MAAM;oBACN,IAAI;oBACJ,OAAO;oBACP,QAAQ,EAAE,IAAI;oBACd,oBAAoB,EAAE,YAAY;iBACnC,CAAA;gBACD,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAA;gBAEtC,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,IAAI,EAAE,4BAA4B,EAAE,OAAO,CAAC,CAAA;gBACxG,MAAK;YACP,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,MAAc,EAAE,IAAU,EAAE,OAAsB,EAAE,WAA6B;QAC1F,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC,IAAI,KAAK,SAAS,CAAC,CAAA;QACrG,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAA;QAEpD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAA;YAC9B,IAAI,CAAC,CAAC,UAAU,KAAK,KAAK,IAAI,UAAU,KAAK,SAAS,CAAC,EAAE,CAAC;gBACxD,SAAQ;YACV,CAAC;YAED,MAAM,eAAe,GAAG,IAAA,yBAAU,EAAC,EAAE,EAAE,IAAI,CAAC,4BAA4B,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YAC1F,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;gBAC7B,IAAA,yBAAU,EAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;oBAC9C,IAAI,EAAE,aAAa;iBACpB,CAAC,CAAA;YACJ,CAAC;YAED,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,GAAG,IAAA,4BAAa,EAAC,IAAI,EAAE,IAAI,CAAC,4BAA4B,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA;YAC5H,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;gBACxB,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,MAAM,CAAC,CAAC,CAAA;gBAC/E,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,MAAM,CAAC,EAAE,YAAY,EAAE,eAAe,EAAE,IAAI,CAAC,CAAA;YACtH,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,EAAE,eAAe,EAAE,IAAI,CAAC,CAAA;YACnE,CAAC;QACH,CAAC;QAED,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,MAAM,OAAO,GAAG,WAAW,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAA;YACzI,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;gBACxB,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAgC,EAAE,IAAI,EAAE,IAAI,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAA;YACpJ,CAAC;YACD,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC,CAAA;QACxE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,IAAI,CAAC,OAAe,EAAE,MAAqB,EAAE,UAAmC,EAAE,IAAiB;QAC/G,IAAI,CAAC,IAAA,2BAAa,GAAE,EAAE,CAAC;YACrB,OAAO,KAAK,CAAA;QACd,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,IAAI,IAAI,CAAA;QAChC,MAAM,OAAO,GAAG,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC,UAAU,CAAA;QACnF,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAA;QAElC,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1B,MAAM,IAAI,wCAAyB,CAAC,yEAAyE,CAAC,CAAA;YAChH,CAAC;YACD,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,oCAAoC,EAAE,EAAE,4BAA4B,CAAC,CAAA;YACxF,OAAO,KAAK,CAAA;QACd,CAAC;QAED,MAAM,YAAY,GAAG,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,YAAY,CAAA;QACpE,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAA;QACjC,MAAM,IAAI,GAAG,YAAY,IAAI,cAAc,CAAA;QAC3C,MAAM,aAAa,GAAG,IAAI,KAAK,aAAa,CAAA;QAC5C,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,KAAK,EAAE,aAAa,CAAC,CAAA;QAElE,IAAI,QAAQ,GAAG,IAAI,CAAA;QACnB,KAAK,MAAM,eAAe,IAAI,gBAAgB,EAAE,CAAC;YAC/C,QAAQ,GAAG,MAAM,IAAA,0BAAY,EAAC,eAAe,EAAE,SAAS,EAAE,YAAY,CAAC,CAAA;YACvE,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;gBACrB,MAAK;YACP,CAAC;QACH,CAAC;QAED,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,CAAC,KAAK,IAAI,CAAC,aAAa,IAAI,YAAY,KAAK,cAAc,EAAE,CAAC;gBAChE,QAAQ,GAAG,MAAM,IAAA,0BAAY,EAAC,eAAe,EAAE,SAAS,EAAE,YAAY,CAAC,CAAA;gBACvE,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;oBACrB,kBAAG,CAAC,IAAI,CAAC,gGAAgG,CAAC,CAAA;gBAC5G,CAAC;YACH,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;gBACtC,MAAM,IAAA,yBAAW,EAAC,KAAK,EAAE,gBAAgB,EAAE,SAAS,EAAE,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAA;gBAC1F,OAAO,KAAK,CAAA;YACd,CAAC;QACH,CAAC;QAED,IAAI,CAAC,IAAA,gCAAiB,GAAE,EAAE,CAAC;YACzB,MAAM,IAAI,wCAAyB,CAAC,+CAA+C,CAAC,CAAA;QACtF,CAAC;QAED,IAAI,MAAM,GAAG,OAAO,CAAC,UAAU,CAAA;QAC/B,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBACvB,MAAM,GAAG,IAAI,CAAA;YACf,CAAC;QACH,CAAC;aAAM,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YAC1B,MAAM,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;QAChD,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;QAEzE,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,SAAS,CAAA;QAC5C,IAAI,QAAQ,EAAE,CAAC;YACb,kHAAkH;YAClH,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAC1B,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAC,WAAW,EAAC,EAAE;gBAC/B,IAAI,MAAM,IAAA,eAAU,EAAC,WAAW,CAAC,EAAE,CAAC;oBAClC,OAAO,WAAW,CAAA;gBACpB,CAAC;gBACD,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,WAAW,CAAC,CAAA;YAC3C,CAAC,CAAC,CACH,CAAA;YACD,kBAAG,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,EAAE,0CAA0C,CAAC,CAAA;QACpE,CAAC;QACD,MAAM,iBAAiB,GAAwC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,IAAI,IAAI,CAAC,4BAA4B,CAAA;QAE5J,MAAM,WAAW,GAAgB;YAC/B,kBAAkB,EAAE,KAAK;YACzB,oEAAoE;YACpE,+IAA+I;YAC/I,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;gBACvB,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;oBACrB,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE,CAAC;wBAC9B,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;4BACtB,OAAO,IAAI,CAAA;wBACb,CAAC;oBACH,CAAC;gBACH,CAAC;gBACD,OAAO,CACL,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;oBACtB,IAAI,CAAC,UAAU,CAAC,mBAAmB,EAAE,OAAO,CAAC,MAAM,CAAC;oBACpD,IAAI,CAAC,QAAQ,CAAC,yCAAyC,CAAC;oBACxD,IAAI,CAAC,QAAQ,CAAC,kDAAkD,CAAC;oBACjE,IAAI,CAAC,QAAQ,CAAC,0CAA0C,CAAC,CAC1D,CAAA;gBAED;;;oBAGI;YACN,CAAC;YACD,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YAC/D,IAAI;YACJ,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ;YAClC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,SAAS;YACjD,GAAG,EAAE,OAAO;YACZ,QAAQ,EAAE,YAAY,IAAI,SAAS;YACnC,QAAQ;YACR,oEAAoE;YACpE,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;YAChD,cAAc,EAAE,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE,iBAAiB,CAAC;YAC/E,mBAAmB,EAAE,iBAAiB,CAAC,mBAAmB,IAAI,SAAS;SACxE,CAAA;QAED,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAA;QAEjD,2FAA2F;QAC3F,IAAI,UAAU,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACzC,MAAM,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,mCAAmC,CAAA;YACtF,MAAM,oBAAoB,GAAG,MAAM,IAAA,0BAAY,EAAC,QAAQ,EAAE,UAAU,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAA;YAC5F,IAAI,oBAAoB,IAAI,IAAI,EAAE,CAAC;gBACjC,MAAM,IAAI,wCAAyB,CAAC,sBAAsB,QAAQ,kFAAkF,CAAC,CAAA;YACvJ,CAAC;YAED,kEAAkE;YAClE,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;YAC5E,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,MAAO,EAAE,YAAY,CAAC,CAAA;YACrD,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,YAAY,EAAE,oBAAoB,EAAE,YAAY,CAAC,CAAA;YAC5E,MAAM,IAAI,CAAC,uBAAuB,CAAC,YAAY,EAAE,IAAI,EAAE,mBAAI,CAAC,GAAG,EAAE,IAAI,CAAC,uBAAuB,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,4BAA4B,CAAC,WAAW,CAAC,CAAC,CAAA;QAChL,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QACjD,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,OAAe,EAAE,KAAc,EAAE,iBAAmC;QAClG,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY,CAAA;QAC5C,MAAM,kBAAkB,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAA;QAEhD,MAAM,eAAe,GAAG,CAAC,QAAgB,EAAE,EAAE;YAC3C,gDAAgD;YAChD,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;gBACzB,IAAI,iBAAiB,CAAC,YAAY,EAAE,CAAC;oBACnC,OAAO,iBAAiB,CAAC,YAAY,CAAA;gBACvC,CAAC;gBACD,MAAM,CAAC,GAAG,gBAAgB,kBAAkB,QAAQ,CAAA;gBACpD,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAA;gBAClD,CAAC;qBAAM,CAAC;oBACN,OAAO,IAAA,6BAAe,EAAC,wBAAwB,CAAC,CAAA;gBAClD,CAAC;YACH,CAAC;YAED,yBAAyB;YACzB,IAAI,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;gBAC5C,OAAO,iBAAiB,CAAC,uBAAuB,CAAA;YAClD,CAAC;YAED,4DAA4D;YAC5D,IAAI,iBAAiB,CAAC,mBAAmB,EAAE,CAAC;gBAC1C,OAAO,iBAAiB,CAAC,mBAAmB,CAAA;YAC9C,CAAC;YACD,MAAM,CAAC,GAAG,gBAAgB,kBAAkB,gBAAgB,CAAA;YAC5D,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAA;YAClD,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAA,6BAAe,EAAC,wBAAwB,CAAC,CAAA;YAClD,CAAC;QACH,CAAC,CAAA;QAED,MAAM,YAAY,GAAG,KAAK,IAAI,IAAI,CAAC,4BAA4B,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAA;QAEzK,uGAAuG;QACvG,MAAM,eAAe,GAAG,KAAK,CAAC,CAAC,CAAC,iBAAiB,CAAC,eAAe,KAAK,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,eAAe,KAAK,KAAK,CAAA;QAExH,MAAM,cAAc,GAA6C,QAAQ,CAAC,EAAE;YAC1E,MAAM,YAAY,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAA;YAC9C,MAAM,IAAI,GAAG;gBACX,YAAY,EAAE,YAAY,IAAI,SAAS;gBACvC,eAAe,EAAE,eAAe,aAAf,eAAe,cAAf,eAAe,GAAI,SAAS;gBAC7C,SAAS,EAAE,iBAAiB,CAAC,SAAS,IAAI,SAAS;gBACnD,YAAY,EAAE,YAAY,IAAI,SAAS;aACxC,CAAA;YACD,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,EAAE,EAAE,2BAA2B,CAAC,CAAA;YACjF,OAAO,IAAI,CAAA;QACb,CAAC,CAAA;QACD,OAAO,cAAc,CAAA;IACvB,CAAC;IAED,kCAAkC;IACxB,KAAK,CAAC,MAAM,CAAC,IAAiB,EAAE,iBAAmC;QAC3E,MAAM,UAAU,GAAG,MAAM,IAAA,kCAAe,EAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,iBAAiB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;QAE3F,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,mBAAmB,EAAE,GAAG,IAAI,CAAA;QACnE,kBAAG,CAAC,IAAI,CACN;YACE,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,GAAG,CAAC;YACvB,QAAQ;YACR,IAAI;YACJ,QAAQ,EAAE,QAAQ,IAAI,MAAM;YAC5B,mBAAmB,EAAE,mBAAmB,IAAI,MAAM;SACnD,EACD,UAAU,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,SAAS,CACjD,CAAA;QAED,OAAO,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAA,oBAAS,EAAC,IAAI,CAAC,CAAA;IAC/E,CAAC;IAED,kCAAkC;IACxB,KAAK,CAAC,MAAM,CAAC,OAAe,EAAE,OAAe,EAAE,QAAkB,EAAE,QAAmC;QAC9G,sDAAsD;QACtD,MAAM,IAAA,gBAAK,EAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;QAEvD,MAAM,IAAI,GAAG,IAAA,6BAAuB,EAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;QACxD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,eAAe,CAAC,CAAA;QAClD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAClB,OAAO,MAAM,IAAA,mBAAI,EAAC,cAAc,EAAE,IAAI,CAAC,CAAA;IACzC,CAAC;IAEM,iBAAiB,CAAC,IAAY;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAA;IAClF,CAAC;IAEM,yBAAyB,CAAC,SAAiB;QAChD,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAA;IACnE,CAAC;IAED,wBAAwB;IACxB,KAAK,CAAC,eAAe,CAAC,QAAa,EAAE,YAAoB;QACvD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,MAAM,WAAW,GAAG,OAAO,CAAC,eAAe,CAAA;QAE3C,oEAAoE;QACpE,QAAQ,CAAC,kBAAkB,GAAG,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAA;QAE7I,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;QACrC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,MAAM,OAAO,GAAG,QAAQ,CAAC,gBAAgB,CAAA;YACzC,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,CAAA;YAC1D,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;gBACpB,MAAM,IAAA,mBAAc,EAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,CAAA;YACzD,CAAC;YACD,MAAM,YAAY,GAAG,WAAW,CAAA;YAChC,QAAQ,CAAC,gBAAgB,GAAG,YAAY,CAAA;YACxC,MAAM,IAAA,aAAQ,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC,CAAA;QAC9D,CAAC;QACD,QAAQ,CAAC,YAAY,GAAG,OAAO,CAAC,WAAW,CAAA;QAC3C,QAAQ,CAAC,mBAAmB,GAAG,OAAO,CAAC,WAAW,CAAA;QAElD,MAAM,oBAAoB,GAAG,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,CAAA;QACnF,IAAI,oBAAoB,IAAI,IAAI,EAAE,CAAC;YACjC,QAAQ,CAAC,sBAAsB,GAAG,oBAAoB,CAAA;QACxD,CAAC;QAED,QAAQ,CAAC,0BAA0B,GAAG,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,IAAI,OAAO,CAAC,OAAO,CAAA;QAC7G,QAAQ,CAAC,eAAe,GAAG,OAAO,CAAC,YAAY,CAAA;QAE/C,IAAA,kBAAG,EAAC,IAAI,CAAC,4BAA4B,CAAC,QAAQ,IAAK,IAAI,CAAC,MAAc,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,yBAAyB,GAAG,EAAE,CAAC,CAAC,CAAA;QACjI,QAAQ,CAAC,wBAAwB,GAAG,OAAO,CAAC,SAAS,CAAA;QAErD,IAAI,IAAI,CAAC,4BAA4B,CAAC,eAAe,EAAE,CAAC;YACtD,QAAQ,CAAC,8BAA8B,GAAG,KAAK,CAAA;QACjD,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,4BAA4B,CAAC,UAAU,CAAA;QAC/D,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;QACrC,CAAC;IACH,CAAC;IAES,KAAK,CAAC,OAAO,CAAC,WAA6B,EAAE,MAAe;QACpE,MAAM,oBAAoB,GAAG,KAAK,EAAE,eAAuB,EAAE,WAAqB,EAAE,MAAiC,EAAoB,EAAE;YACzI,MAAM,sBAAe,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,EAAE,IAAY,EAAiB,EAAE;gBAC3E,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;oBACjB,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;gBACrE,CAAC;gBACD,OAAO,IAAI,CAAA;YACb,CAAC,CAAC,CAAA;YACF,OAAO,IAAI,CAAA;QACb,CAAC,CAAA;QAED,MAAM,WAAW,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,MAAM,CAAA;QACzD,MAAM,oBAAoB,CAAC,WAAW,CAAC,SAAS,EAAE,MAAM,IAAA,kBAAO,EAAC,WAAW,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QACrH,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,IAAI,CAAA;QACb,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,WAAW,EAAE,mBAAmB,CAAC,CAAA;QAC1F,MAAM,oBAAoB,CAAC,eAAe,EAAE,MAAM,IAAA,0BAAgB,EAAC,IAAA,kBAAO,EAAC,eAAe,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAA;QAEhI,OAAO,IAAI,CAAA;IACb,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,OAAe,EAAE,YAA8B;QAC9E,MAAM,eAAe,GAAG,YAAY,CAAC,QAAQ,CAAA;QAC7C,IAAI,eAAe,KAAK,KAAK,EAAE,CAAC;YAC9B,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,gDAAgD,EAAE,EAAE,4BAA4B,CAAC,CAAA;YACpG,OAAM;QACR,CAAC;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAA;QAChD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,gDAAgD,EAAE,EAAE,4BAA4B,CAAC,CAAA;YACpG,OAAM;QACR,CAAC;QACD,MAAM,IAAA,mBAAQ,EAAC,OAAO,CAAC,CAAA;QACvB,kBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,yBAAyB,CAAC,CAAA;IAC3C,CAAC;IAEO,kBAAkB,CAAC,OAAe;QACxC,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAA;QACpC,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAA;QAE/D,kCAAkC;QAClC,IAAI,OAAO,IAAI,eAAe,EAAE,CAAC;YAC/B,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,wCAAyB,CAAC,kCAAkC,CAAC,CAAA;YACzE,CAAC;YACD,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,MAAM,IAAI,wCAAyB,CAAC,qDAAqD,CAAC,CAAA;YAC5F,CAAC;YACD,OAAO,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,CAAA;QAC5E,CAAC;QAED,oBAAoB;QACpB,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAA;QAC7C,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAA;QAClD,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAA;QACnD,IAAI,WAAW,IAAI,aAAa,IAAI,cAAc,EAAE,CAAC;YACnD,IAAI,CAAC,WAAW,IAAI,CAAC,aAAa,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtD,MAAM,IAAI,wCAAyB,CAAC,8EAA8E,CAAC,CAAA;YACrH,CAAC;YACD,OAAO,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE,cAAc,EAAE,CAAC,CAAA;QACzG,CAAC;QAED,qBAAqB;QACrB,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAA;QAC3C,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAA;QAC1D,IAAI,eAAe,EAAE,CAAC;YACpB,IAAI,IAAI,GAAkC,EAAE,eAAe,EAAE,CAAA;YAC7D,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,GAAG,EAAE,GAAG,IAAI,EAAE,QAAQ,EAAE,CAAA;YAC9B,CAAC;YACD,OAAO,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;QAC/D,CAAC;QAED,4CAA4C;QAC5C,OAAO,SAAS,CAAA;IAClB,CAAC;IAEO,uBAAuB,CAAC,OAAe,EAAE,WAA+C,EAAE,eAAuC;QACvI,MAAM,OAAO,GAAG,IAAI,CAAC,4BAA4B,CAAC,QAAQ,CAAA;QAC1D,IAAI,OAAO,OAAO,KAAK,SAAS,IAAI,WAAW,EAAE,CAAC;YAChD,MAAM,IAAI,GAA+B;gBACvC,OAAO;gBACP,GAAG,WAAW;gBACd,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;aAC7B,CAAA;YACD,OAAO,IAAI,CAAA;QACb,CAAC;QACD,MAAM,MAAM,GAAI,OAAiC,aAAjC,OAAO,uBAAP,OAAO,CAA4B,MAAM,CAAA;QACzD,IAAI,CAAC,MAAM,IAAI,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,eAAe,CAAC,EAAE,CAAC;YACrE,MAAM,IAAI,GAA2B;gBACnC,OAAO;gBACP,GAAG,CAAC,WAAW,aAAX,WAAW,cAAX,WAAW,GAAI,eAAgB,CAAC;gBACpC,MAAM;aACP,CAAA;YACD,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,IAAI,EAAE,CAAA;QACxC,CAAC;QACD,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,OAAgC,CAAA;YACrE,OAAO;gBACL,OAAO;gBACP,GAAG,WAAW;gBACd,WAAW,EAAE,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;gBAC3C,WAAW,EAAE,WAAW,IAAI,SAAS;aACtC,CAAA;QACH,CAAC;QACD,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO;gBACL,IAAI,EAAE,YAAY;gBAClB,OAAO;gBACP,GAAG,eAAe;aACnB,CAAA;QACH,CAAC;QACD,OAAO,SAAS,CAAA;IAClB,CAAC;CACF;AA7iBD,8BA6iBC;AAED,SAAS,mBAAmB,CAAC,KAAc,EAAE,aAAsB;IACjE,IAAI,aAAa,EAAE,CAAC;QAClB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,0BAA0B,CAAC,CAAA;IACvG,CAAC;IACD,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,oBAAoB,EAAE,qCAAqC,CAAC,CAAC,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAA;AAC7G,CAAC", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { deepAssign, Arch, AsyncTaskManager, exec, InvalidConfigurationError, log, use, getArchSuffix } from \"builder-util\"\nimport { signAsync } from \"@electron/osx-sign\"\nimport { PerFileSignOptions, SignOptions } from \"@electron/osx-sign/dist/cjs/types\"\nimport { mkdir, readdir } from \"fs/promises\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { copyFile, statOrNull, unlinkIfExists } from \"builder-util/out/fs\"\nimport { orIfFileNotExist } from \"builder-util/out/promise\"\nimport { AppInfo } from \"./appInfo\"\nimport { CertType, CodeSigningInfo, createKeychain, findIdentity, Identity, isSignAllowed, removeKeychain, reportError } from \"./codeSign/macCodeSign\"\nimport { DIR_TARGET, Platform, Target } from \"./core\"\nimport { AfterPackContext, ElectronPlatformName } from \"./index\"\nimport { MacConfiguration, MasConfiguration, NotarizeLegacyOptions, NotarizeNotaryOptions } from \"./options/macOptions\"\nimport { Packager } from \"./packager\"\nimport { chooseNotNull, resolveFunction, PlatformPackager } from \"./platformPackager\"\nimport { ArchiveTarget } from \"./targets/ArchiveTarget\"\nimport { PkgTarget, prepareProductBuildArgs } from \"./targets/pkg\"\nimport { createCommonTarget, NoOpTarget } from \"./targets/targetFactory\"\nimport { isMacOsHighSierra } from \"./util/macosVersion\"\nimport { getTemplatePath } from \"./util/pathManager\"\nimport * as fs from \"fs/promises\"\nimport { notarize, NotarizeOptions } from \"@electron/notarize\"\nimport {\n  LegacyNotarizePasswordCredentials,\n  LegacyNotarizeStartOptions,\n  NotaryToolStartOptions,\n  NotaryToolCredentials,\n  NotaryToolKeychainCredentials,\n} from \"@electron/notarize/lib/types\"\n\nexport type CustomMacSignOptions = SignOptions\nexport type CustomMacSign = (configuration: CustomMacSignOptions, packager: MacPackager) => Promise<void>\n\nexport default class MacPackager extends PlatformPackager<MacConfiguration> {\n  readonly codeSigningInfo = new Lazy<CodeSigningInfo>(() => {\n    const cscLink = this.getCscLink()\n    if (cscLink == null || process.platform !== \"darwin\") {\n      return Promise.resolve({ keychainFile: process.env.CSC_KEYCHAIN || null })\n    }\n\n    return createKeychain({\n      tmpDir: this.info.tempDirManager,\n      cscLink,\n      cscKeyPassword: this.getCscPassword(),\n      cscILink: chooseNotNull(this.platformSpecificBuildOptions.cscInstallerLink, process.env.CSC_INSTALLER_LINK),\n      cscIKeyPassword: chooseNotNull(this.platformSpecificBuildOptions.cscInstallerKeyPassword, process.env.CSC_INSTALLER_KEY_PASSWORD),\n      currentDir: this.projectDir,\n    }).then(result => {\n      const keychainFile = result.keychainFile\n      if (keychainFile != null) {\n        this.info.disposeOnBuildFinish(() => removeKeychain(keychainFile))\n      }\n      return result\n    })\n  })\n\n  private _iconPath = new Lazy(() => this.getOrConvertIcon(\"icns\"))\n\n  constructor(info: Packager) {\n    super(info, Platform.MAC)\n  }\n\n  get defaultTarget(): Array<string> {\n    return this.info.framework.macOsDefaultTargets\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  protected prepareAppInfo(appInfo: AppInfo): AppInfo {\n    // codesign requires the filename to be normalized to the NFD form\n    return new AppInfo(this.info, this.platformSpecificBuildOptions.bundleVersion, this.platformSpecificBuildOptions, true)\n  }\n\n  async getIconPath(): Promise<string | null> {\n    return this._iconPath.value\n  }\n\n  createTargets(targets: Array<string>, mapper: (name: string, factory: (outDir: string) => Target) => void): void {\n    for (const name of targets) {\n      switch (name) {\n        case DIR_TARGET:\n          break\n\n        case \"dmg\": {\n          // eslint-disable-next-line @typescript-eslint/no-var-requires\n          const { DmgTarget } = require(\"dmg-builder\")\n          mapper(name, outDir => new DmgTarget(this, outDir))\n          break\n        }\n\n        case \"zip\":\n          // https://github.com/electron-userland/electron-builder/issues/2313\n          mapper(name, outDir => new ArchiveTarget(name, outDir, this, true))\n          break\n\n        case \"pkg\":\n          mapper(name, outDir => new PkgTarget(this, outDir))\n          break\n\n        default:\n          mapper(name, outDir => (name === \"mas\" || name === \"mas-dev\" ? new NoOpTarget(name) : createCommonTarget(name, outDir, this)))\n          break\n      }\n    }\n  }\n\n  protected async doPack(\n    outDir: string,\n    appOutDir: string,\n    platformName: ElectronPlatformName,\n    arch: Arch,\n    platformSpecificBuildOptions: MacConfiguration,\n    targets: Array<Target>\n  ): Promise<any> {\n    switch (arch) {\n      default: {\n        return super.doPack(outDir, appOutDir, platformName, arch, platformSpecificBuildOptions, targets)\n      }\n      case Arch.universal: {\n        const outDirName = (arch: Arch) => `${appOutDir}-${Arch[arch]}-temp`\n\n        const x64Arch = Arch.x64\n        const x64AppOutDir = outDirName(x64Arch)\n        await super.doPack(outDir, x64AppOutDir, platformName, x64Arch, platformSpecificBuildOptions, targets, false, true)\n        const arm64Arch = Arch.arm64\n        const arm64AppOutPath = outDirName(arm64Arch)\n        await super.doPack(outDir, arm64AppOutPath, platformName, arm64Arch, platformSpecificBuildOptions, targets, false, true)\n        const framework = this.info.framework\n        log.info(\n          {\n            platform: platformName,\n            arch: Arch[arch],\n            [`${framework.name}`]: framework.version,\n            appOutDir: log.filePath(appOutDir),\n          },\n          `packaging`\n        )\n        const appFile = `${this.appInfo.productFilename}.app`\n        const { makeUniversalApp } = require(\"@electron/universal\")\n        await makeUniversalApp({\n          x64AppPath: path.join(x64AppOutDir, appFile),\n          arm64AppPath: path.join(arm64AppOutPath, appFile),\n          outAppPath: path.join(appOutDir, appFile),\n          force: true,\n          mergeASARs: platformSpecificBuildOptions.mergeASARs ?? true,\n          singleArchFiles: platformSpecificBuildOptions.singleArchFiles,\n          x64ArchFiles: platformSpecificBuildOptions.x64ArchFiles,\n        })\n        await fs.rm(x64AppOutDir, { recursive: true, force: true })\n        await fs.rm(arm64AppOutPath, { recursive: true, force: true })\n\n        // Give users a final opportunity to perform things on the combined universal package before signing\n        const packContext: AfterPackContext = {\n          appOutDir,\n          outDir,\n          arch,\n          targets,\n          packager: this,\n          electronPlatformName: platformName,\n        }\n        await this.info.afterPack(packContext)\n\n        await this.doSignAfterPack(outDir, appOutDir, platformName, arch, platformSpecificBuildOptions, targets)\n        break\n      }\n    }\n  }\n\n  async pack(outDir: string, arch: Arch, targets: Array<Target>, taskManager: AsyncTaskManager): Promise<void> {\n    const hasMas = targets.length !== 0 && targets.some(it => it.name === \"mas\" || it.name === \"mas-dev\")\n    const prepackaged = this.packagerOptions.prepackaged\n\n    for (const target of targets) {\n      const targetName = target.name\n      if (!(targetName === \"mas\" || targetName === \"mas-dev\")) {\n        continue\n      }\n\n      const masBuildOptions = deepAssign({}, this.platformSpecificBuildOptions, this.config.mas)\n      if (targetName === \"mas-dev\") {\n        deepAssign(masBuildOptions, this.config.masDev, {\n          type: \"development\",\n        })\n      }\n\n      const targetOutDir = path.join(outDir, `${targetName}${getArchSuffix(arch, this.platformSpecificBuildOptions.defaultArch)}`)\n      if (prepackaged == null) {\n        await this.doPack(outDir, targetOutDir, \"mas\", arch, masBuildOptions, [target])\n        await this.sign(path.join(targetOutDir, `${this.appInfo.productFilename}.app`), targetOutDir, masBuildOptions, arch)\n      } else {\n        await this.sign(prepackaged, targetOutDir, masBuildOptions, arch)\n      }\n    }\n\n    if (!hasMas || targets.length > 1) {\n      const appPath = prepackaged == null ? path.join(this.computeAppOutDir(outDir, arch), `${this.appInfo.productFilename}.app`) : prepackaged\n      if (prepackaged == null) {\n        await this.doPack(outDir, path.dirname(appPath), this.platform.nodeName as ElectronPlatformName, arch, this.platformSpecificBuildOptions, targets)\n      }\n      this.packageInDistributableFormat(appPath, arch, targets, taskManager)\n    }\n  }\n\n  private async sign(appPath: string, outDir: string | null, masOptions: MasConfiguration | null, arch: Arch | null): Promise<boolean> {\n    if (!isSignAllowed()) {\n      return false\n    }\n\n    const isMas = masOptions != null\n    const options = masOptions == null ? this.platformSpecificBuildOptions : masOptions\n    const qualifier = options.identity\n\n    if (qualifier === null) {\n      if (this.forceCodeSigning) {\n        throw new InvalidConfigurationError(\"identity explicitly is set to null, but forceCodeSigning is set to true\")\n      }\n      log.info({ reason: \"identity explicitly is set to null\" }, \"skipped macOS code signing\")\n      return false\n    }\n\n    const keychainFile = (await this.codeSigningInfo.value).keychainFile\n    const explicitType = options.type\n    const type = explicitType || \"distribution\"\n    const isDevelopment = type === \"development\"\n    const certificateTypes = getCertificateTypes(isMas, isDevelopment)\n\n    let identity = null\n    for (const certificateType of certificateTypes) {\n      identity = await findIdentity(certificateType, qualifier, keychainFile)\n      if (identity != null) {\n        break\n      }\n    }\n\n    if (identity == null) {\n      if (!isMas && !isDevelopment && explicitType !== \"distribution\") {\n        identity = await findIdentity(\"Mac Developer\", qualifier, keychainFile)\n        if (identity != null) {\n          log.warn(\"Mac Developer is used to sign app — it is only for development and testing, not for production\")\n        }\n      }\n\n      if (!options.sign && identity == null) {\n        await reportError(isMas, certificateTypes, qualifier, keychainFile, this.forceCodeSigning)\n        return false\n      }\n    }\n\n    if (!isMacOsHighSierra()) {\n      throw new InvalidConfigurationError(\"macOS High Sierra 10.13.6 is required to sign\")\n    }\n\n    let filter = options.signIgnore\n    if (Array.isArray(filter)) {\n      if (filter.length == 0) {\n        filter = null\n      }\n    } else if (filter != null) {\n      filter = filter.length === 0 ? null : [filter]\n    }\n\n    const filterRe = filter == null ? null : filter.map(it => new RegExp(it))\n\n    let binaries = options.binaries || undefined\n    if (binaries) {\n      // Accept absolute paths for external binaries, else resolve relative paths from the artifact's app Contents path.\n      binaries = await Promise.all(\n        binaries.map(async destination => {\n          if (await statOrNull(destination)) {\n            return destination\n          }\n          return path.resolve(appPath, destination)\n        })\n      )\n      log.info({ binaries }, \"signing additional user-defined binaries\")\n    }\n    const customSignOptions: MasConfiguration | MacConfiguration = (isMas ? masOptions : this.platformSpecificBuildOptions) || this.platformSpecificBuildOptions\n\n    const signOptions: SignOptions = {\n      identityValidation: false,\n      // https://github.com/electron-userland/electron-builder/issues/1699\n      // kext are signed by the chipset manufacturers. You need a special certificate (only available on request) from Apple to be able to sign kext.\n      ignore: (file: string) => {\n        if (filterRe != null) {\n          for (const regExp of filterRe) {\n            if (regExp.test(file)) {\n              return true\n            }\n          }\n        }\n        return (\n          file.endsWith(\".kext\") ||\n          file.startsWith(\"/Contents/PlugIns\", appPath.length) ||\n          file.includes(\"/node_modules/puppeteer/.local-chromium\") ||\n          file.includes(\"/node_modules/playwright-firefox/.local-browsers\") ||\n          file.includes(\"/node_modules/playwright/.local-browsers\")\n        )\n\n        /* Those are browser automating modules, browser (chromium, nightly) cannot be signed\n          https://github.com/electron-userland/electron-builder/issues/2010\n          https://github.com/electron-userland/electron-builder/issues/5383\n          */\n      },\n      identity: identity ? identity.hash || identity.name : undefined,\n      type,\n      platform: isMas ? \"mas\" : \"darwin\",\n      version: this.config.electronVersion || undefined,\n      app: appPath,\n      keychain: keychainFile || undefined,\n      binaries,\n      // https://github.com/electron-userland/electron-builder/issues/1480\n      strictVerify: options.strictVerify,\n      preAutoEntitlements: options.preAutoEntitlements,\n      optionsForFile: await this.getOptionsForFile(appPath, isMas, customSignOptions),\n      provisioningProfile: customSignOptions.provisioningProfile || undefined,\n    }\n\n    await this.doSign(signOptions, customSignOptions)\n\n    // https://github.com/electron-userland/electron-builder/issues/1196#issuecomment-312310209\n    if (masOptions != null && !isDevelopment) {\n      const certType = isDevelopment ? \"Mac Developer\" : \"3rd Party Mac Developer Installer\"\n      const masInstallerIdentity = await findIdentity(certType, masOptions.identity, keychainFile)\n      if (masInstallerIdentity == null) {\n        throw new InvalidConfigurationError(`Cannot find valid \"${certType}\" identity to sign MAS installer, please see https://electron.build/code-signing`)\n      }\n\n      // mas uploaded to AppStore, so, use \"-\" instead of space for name\n      const artifactName = this.expandArtifactNamePattern(masOptions, \"pkg\", arch)\n      const artifactPath = path.join(outDir!, artifactName)\n      await this.doFlat(appPath, artifactPath, masInstallerIdentity, keychainFile)\n      await this.dispatchArtifactCreated(artifactPath, null, Arch.x64, this.computeSafeArtifactName(artifactName, \"pkg\", arch, true, this.platformSpecificBuildOptions.defaultArch))\n    }\n\n    if (!isMas) {\n      await this.notarizeIfProvided(appPath, options)\n    }\n    return true\n  }\n\n  private async getOptionsForFile(appPath: string, isMas: boolean, customSignOptions: MacConfiguration) {\n    const resourceList = await this.resourceList\n    const entitlementsSuffix = isMas ? \"mas\" : \"mac\"\n\n    const getEntitlements = (filePath: string) => {\n      // check if root app, then use main entitlements\n      if (filePath === appPath) {\n        if (customSignOptions.entitlements) {\n          return customSignOptions.entitlements\n        }\n        const p = `entitlements.${entitlementsSuffix}.plist`\n        if (resourceList.includes(p)) {\n          return path.join(this.info.buildResourcesDir, p)\n        } else {\n          return getTemplatePath(\"entitlements.mac.plist\")\n        }\n      }\n\n      // It's a login helper...\n      if (filePath.includes(\"Library/LoginItems\")) {\n        return customSignOptions.entitlementsLoginHelper\n      }\n\n      // Only remaining option is that it's inherited entitlements\n      if (customSignOptions.entitlementsInherit) {\n        return customSignOptions.entitlementsInherit\n      }\n      const p = `entitlements.${entitlementsSuffix}.inherit.plist`\n      if (resourceList.includes(p)) {\n        return path.join(this.info.buildResourcesDir, p)\n      } else {\n        return getTemplatePath(\"entitlements.mac.plist\")\n      }\n    }\n\n    const requirements = isMas || this.platformSpecificBuildOptions.requirements == null ? undefined : await this.getResource(this.platformSpecificBuildOptions.requirements)\n\n    // harden by default for mac builds. Only harden mas builds if explicitly true (backward compatibility)\n    const hardenedRuntime = isMas ? customSignOptions.hardenedRuntime === true : customSignOptions.hardenedRuntime !== false\n\n    const optionsForFile: (filePath: string) => PerFileSignOptions = filePath => {\n      const entitlements = getEntitlements(filePath)\n      const args = {\n        entitlements: entitlements || undefined,\n        hardenedRuntime: hardenedRuntime ?? undefined,\n        timestamp: customSignOptions.timestamp || undefined,\n        requirements: requirements || undefined,\n      }\n      log.debug({ file: log.filePath(filePath), ...args }, \"selecting signing options\")\n      return args\n    }\n    return optionsForFile\n  }\n\n  //noinspection JSMethodCanBeStatic\n  protected async doSign(opts: SignOptions, customSignOptions: MacConfiguration): Promise<void> {\n    const customSign = await resolveFunction(this.appInfo.type, customSignOptions.sign, \"sign\")\n\n    const { app, platform, type, identity, provisioningProfile } = opts\n    log.info(\n      {\n        file: log.filePath(app),\n        platform,\n        type,\n        identity: identity || \"none\",\n        provisioningProfile: provisioningProfile || \"none\",\n      },\n      customSign ? \"executing custom sign\" : \"signing\"\n    )\n\n    return customSign ? Promise.resolve(customSign(opts, this)) : signAsync(opts)\n  }\n\n  //noinspection JSMethodCanBeStatic\n  protected async doFlat(appPath: string, outFile: string, identity: Identity, keychain: string | null | undefined): Promise<any> {\n    // productbuild doesn't created directory for out file\n    await mkdir(path.dirname(outFile), { recursive: true })\n\n    const args = prepareProductBuildArgs(identity, keychain)\n    args.push(\"--component\", appPath, \"/Applications\")\n    args.push(outFile)\n    return await exec(\"productbuild\", args)\n  }\n\n  public getElectronSrcDir(dist: string) {\n    return path.resolve(this.projectDir, dist, this.info.framework.distMacOsAppName)\n  }\n\n  public getElectronDestinationDir(appOutDir: string) {\n    return path.join(appOutDir, this.info.framework.distMacOsAppName)\n  }\n\n  // todo fileAssociations\n  async applyCommonInfo(appPlist: any, contentsPath: string) {\n    const appInfo = this.appInfo\n    const appFilename = appInfo.productFilename\n\n    // https://github.com/electron-userland/electron-builder/issues/1278\n    appPlist.CFBundleExecutable = appFilename.endsWith(\" Helper\") ? appFilename.substring(0, appFilename.length - \" Helper\".length) : appFilename\n\n    const icon = await this.getIconPath()\n    if (icon != null) {\n      const oldIcon = appPlist.CFBundleIconFile\n      const resourcesPath = path.join(contentsPath, \"Resources\")\n      if (oldIcon != null) {\n        await unlinkIfExists(path.join(resourcesPath, oldIcon))\n      }\n      const iconFileName = \"icon.icns\"\n      appPlist.CFBundleIconFile = iconFileName\n      await copyFile(icon, path.join(resourcesPath, iconFileName))\n    }\n    appPlist.CFBundleName = appInfo.productName\n    appPlist.CFBundleDisplayName = appInfo.productName\n\n    const minimumSystemVersion = this.platformSpecificBuildOptions.minimumSystemVersion\n    if (minimumSystemVersion != null) {\n      appPlist.LSMinimumSystemVersion = minimumSystemVersion\n    }\n\n    appPlist.CFBundleShortVersionString = this.platformSpecificBuildOptions.bundleShortVersion || appInfo.version\n    appPlist.CFBundleVersion = appInfo.buildVersion\n\n    use(this.platformSpecificBuildOptions.category || (this.config as any).category, it => (appPlist.LSApplicationCategoryType = it))\n    appPlist.NSHumanReadableCopyright = appInfo.copyright\n\n    if (this.platformSpecificBuildOptions.darkModeSupport) {\n      appPlist.NSRequiresAquaSystemAppearance = false\n    }\n\n    const extendInfo = this.platformSpecificBuildOptions.extendInfo\n    if (extendInfo != null) {\n      Object.assign(appPlist, extendInfo)\n    }\n  }\n\n  protected async signApp(packContext: AfterPackContext, isAsar: boolean): Promise<boolean> {\n    const readDirectoryAndSign = async (sourceDirectory: string, directories: string[], filter: (file: string) => boolean): Promise<boolean> => {\n      await BluebirdPromise.map(directories, async (file: string): Promise<null> => {\n        if (filter(file)) {\n          await this.sign(path.join(sourceDirectory, file), null, null, null)\n        }\n        return null\n      })\n      return true\n    }\n\n    const appFileName = `${this.appInfo.productFilename}.app`\n    await readDirectoryAndSign(packContext.appOutDir, await readdir(packContext.appOutDir), file => file === appFileName)\n    if (!isAsar) {\n      return true\n    }\n\n    const outResourcesDir = path.join(packContext.appOutDir, \"resources\", \"app.asar.unpacked\")\n    await readDirectoryAndSign(outResourcesDir, await orIfFileNotExist(readdir(outResourcesDir), []), file => file.endsWith(\".app\"))\n\n    return true\n  }\n\n  private async notarizeIfProvided(appPath: string, buildOptions: MacConfiguration) {\n    const notarizeOptions = buildOptions.notarize\n    if (notarizeOptions === false) {\n      log.info({ reason: \"`notarize` options were set explicitly `false`\" }, \"skipped macOS notarization\")\n      return\n    }\n    const options = this.getNotarizeOptions(appPath)\n    if (!options) {\n      log.warn({ reason: \"`notarize` options were unable to be generated\" }, \"skipped macOS notarization\")\n      return\n    }\n    await notarize(options)\n    log.info(null, \"notarization successful\")\n  }\n\n  private getNotarizeOptions(appPath: string) {\n    const appleId = process.env.APPLE_ID\n    const appleIdPassword = process.env.APPLE_APP_SPECIFIC_PASSWORD\n\n    // option 1: app specific password\n    if (appleId || appleIdPassword) {\n      if (!appleId) {\n        throw new InvalidConfigurationError(`APPLE_ID env var needs to be set`)\n      }\n      if (!appleIdPassword) {\n        throw new InvalidConfigurationError(`APPLE_APP_SPECIFIC_PASSWORD env var needs to be set`)\n      }\n      return this.generateNotarizeOptions(appPath, { appleId, appleIdPassword })\n    }\n\n    // option 2: API key\n    const appleApiKey = process.env.APPLE_API_KEY\n    const appleApiKeyId = process.env.APPLE_API_KEY_ID\n    const appleApiIssuer = process.env.APPLE_API_ISSUER\n    if (appleApiKey || appleApiKeyId || appleApiIssuer) {\n      if (!appleApiKey || !appleApiKeyId || !appleApiIssuer) {\n        throw new InvalidConfigurationError(`Env vars APPLE_API_KEY, APPLE_API_KEY_ID and APPLE_API_ISSUER need to be set`)\n      }\n      return this.generateNotarizeOptions(appPath, undefined, { appleApiKey, appleApiKeyId, appleApiIssuer })\n    }\n\n    // option 3: keychain\n    const keychain = process.env.APPLE_KEYCHAIN\n    const keychainProfile = process.env.APPLE_KEYCHAIN_PROFILE\n    if (keychainProfile) {\n      let args: NotaryToolKeychainCredentials = { keychainProfile }\n      if (keychain) {\n        args = { ...args, keychain }\n      }\n      return this.generateNotarizeOptions(appPath, undefined, args)\n    }\n\n    // if no credentials provided, skip silently\n    return undefined\n  }\n\n  private generateNotarizeOptions(appPath: string, legacyLogin?: LegacyNotarizePasswordCredentials, notaryToolLogin?: NotaryToolCredentials): NotarizeOptions | undefined {\n    const options = this.platformSpecificBuildOptions.notarize\n    if (typeof options === \"boolean\" && legacyLogin) {\n      const proj: LegacyNotarizeStartOptions = {\n        appPath,\n        ...legacyLogin,\n        appBundleId: this.appInfo.id,\n      }\n      return proj\n    }\n    const teamId = (options as NotarizeNotaryOptions)?.teamId\n    if ((teamId || options === true) && (legacyLogin || notaryToolLogin)) {\n      const proj: NotaryToolStartOptions = {\n        appPath,\n        ...(legacyLogin ?? notaryToolLogin!),\n        teamId,\n      }\n      return { tool: \"notarytool\", ...proj }\n    }\n    if (legacyLogin) {\n      const { appBundleId, ascProvider } = options as NotarizeLegacyOptions\n      return {\n        appPath,\n        ...legacyLogin,\n        appBundleId: appBundleId || this.appInfo.id,\n        ascProvider: ascProvider || undefined,\n      }\n    }\n    if (notaryToolLogin) {\n      return {\n        tool: \"notarytool\",\n        appPath,\n        ...notaryToolLogin,\n      }\n    }\n    return undefined\n  }\n}\n\nfunction getCertificateTypes(isMas: boolean, isDevelopment: boolean): CertType[] {\n  if (isDevelopment) {\n    return isMas ? [\"Mac Developer\", \"Apple Development\"] : [\"Mac Developer\", \"Developer ID Application\"]\n  }\n  return isMas ? [\"Apple Distribution\", \"3rd Party Mac Developer Application\"] : [\"Developer ID Application\"]\n}\n"]}