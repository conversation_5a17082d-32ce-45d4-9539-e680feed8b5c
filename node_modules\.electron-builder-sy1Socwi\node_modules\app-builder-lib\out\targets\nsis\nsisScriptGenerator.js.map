{"version": 3, "file": "nsisScriptGenerator.js", "sourceRoot": "", "sources": ["../../../src/targets/nsis/nsisScriptGenerator.ts"], "names": [], "mappings": ";;;AAAA,MAAa,mBAAmB;IAAhC;QACmB,UAAK,GAAkB,EAAE,CAAA;IA0C5C,CAAC;IAxCC,aAAa,CAAC,IAAY;QACxB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,IAAI,GAAG,CAAC,CAAA;IAC7C,CAAC;IAED,YAAY,CAAC,UAAkB,EAAE,GAAW;QAC1C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,kBAAkB,UAAU,KAAK,GAAG,GAAG,CAAC,CAAA;IAC1D,CAAC;IAED,OAAO,CAAC,IAAY;QAClB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,IAAI,GAAG,CAAC,CAAA;IACvC,CAAC;IAED,KAAK,CAAC,IAAY,EAAE,KAA0C;QAC5D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,aAAa,CAAC,CAAA;IACpH,CAAC;IAED,IAAI,CAAC,UAAyB,EAAE,IAAY;QAC1C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,UAAU,GAAG,KAAK,IAAI,GAAG,CAAC,CAAA;IACzF,CAAC;IAED,WAAW,CAAC,IAAY,EAAE,UAAkB;QAC1C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,UAAU,EAAE,CAAC,CAAA;IACvD,CAAC;IAED,iBAAiB;IACjB,KAAK,CAAC,KAAoB;QACxB,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE,CAAC;YAC7B,MAAM,YAAY,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,CAAA;YACpG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,YAAY;oCACT,QAAQ;;;UAGlC,YAAY,SAAS,YAAY;CAC1C,CAAC,CAAA;QACE,CAAC;IACH,CAAC;IAED,KAAK;QACH,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAA;IACrC,CAAC;CACF;AA3CD,kDA2CC;AAED,SAAS,iBAAiB,CAAC,QAAgB;IACzC,IAAI,QAAQ,KAAK,UAAU,EAAE,CAAC;QAC5B,OAAO,eAAe,CAAA;IACxB,CAAC;IACD,IAAI,QAAQ,KAAK,aAAa,EAAE,CAAC;QAC/B,OAAO,kBAAkB,CAAA;IAC3B,CAAC;IACD,OAAO,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;AACjE,CAAC", "sourcesContent": ["export class NsisScriptGenerator {\n  private readonly lines: Array<string> = []\n\n  addIncludeDir(file: string) {\n    this.lines.push(`!addincludedir \"${file}\"`)\n  }\n\n  addPluginDir(pluginArch: string, dir: string) {\n    this.lines.push(`!addplugindir /${pluginArch} \"${dir}\"`)\n  }\n\n  include(file: string) {\n    this.lines.push(`!include \"${file}\"`)\n  }\n\n  macro(name: string, lines: Array<string> | NsisScriptGenerator) {\n    this.lines.push(`!macro ${name}`, `  ${(Array.isArray(lines) ? lines : lines.lines).join(\"\\n  \")}`, `!macroend\\n`)\n  }\n\n  file(outputName: string | null, file: string) {\n    this.lines.push(`File${outputName == null ? \"\" : ` \"/oname=${outputName}\"`} \"${file}\"`)\n  }\n\n  insertMacro(name: string, parameters: string) {\n    this.lines.push(`!insertmacro ${name} ${parameters}`)\n  }\n\n  // without -- !!!\n  flags(flags: Array<string>) {\n    for (const flagName of flags) {\n      const variableName = getVarNameForFlag(flagName).replace(/[-]+(\\w|$)/g, (m, p1) => p1.toUpperCase())\n      this.lines.push(`!macro _${variableName} _a _b _t _f\n  $\\{StdUtils.TestParameter} $R9 \"${flagName}\"\n  StrCmp \"$R9\" \"true\" \\`$\\{_t}\\` \\`$\\{_f}\\`\n!macroend\n!define ${variableName} \\`\"\" ${variableName} \"\"\\`\n`)\n    }\n  }\n\n  build() {\n    return this.lines.join(\"\\n\") + \"\\n\"\n  }\n}\n\nfunction getVarNameForFlag(flagName: string): string {\n  if (flagName === \"allusers\") {\n    return \"isForAllUsers\"\n  }\n  if (flagName === \"currentuser\") {\n    return \"isForCurrentUser\"\n  }\n  return \"is\" + flagName[0].toUpperCase() + flagName.substring(1)\n}\n"]}