{"version": 3, "file": "timer.js", "sourceRoot": "", "sources": ["../../src/util/timer.ts"], "names": [], "mappings": ";;;AAAA,+CAAoC;AAMpC,MAAa,QAAQ;IAGnB,YAA6B,KAAa;QAAb,UAAK,GAAL,KAAK,CAAQ;QAFlC,UAAK,GAAG,OAAO,CAAC,MAAM,EAAE,CAAA;IAEa,CAAC;IAE9C,SAAS;QACP,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACtC,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAA;IACvD,CAAC;IAED,GAAG;QACD,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;IACpD,CAAC;CACF;AAbD,4BAaC;AAED,MAAM,eAAe;IACnB,GAAG;QACD,SAAS;IACX,CAAC;CACF;AAED,SAAgB,IAAI,CAAC,KAAa;IAChC,OAAO,oBAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,eAAe,EAAE,CAAA;AACpE,CAAC;AAFD,oBAEC", "sourcesContent": ["import { debug } from \"builder-util\"\n\nexport interface Timer {\n  end(): void\n}\n\nexport class DevTimer implements Timer {\n  private start = process.hrtime()\n\n  constructor(private readonly label: string) {}\n\n  endAndGet(): string {\n    const end = process.hrtime(this.start)\n    return `${end[0]}s ${Math.round(end[1] / 1000000)}ms`\n  }\n\n  end(): void {\n    console.info(`${this.label}: ${this.endAndGet()}`)\n  }\n}\n\nclass ProductionTimer implements Timer {\n  end(): void {\n    // ignore\n  }\n}\n\nexport function time(label: string): Timer {\n  return debug.enabled ? new DevTimer(label) : new ProductionTimer()\n}\n"]}